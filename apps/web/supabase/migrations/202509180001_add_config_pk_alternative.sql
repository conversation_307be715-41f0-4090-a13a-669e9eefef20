-- Alternative approach: Temporarily disable publication during migration
-- This is more aggressive but safer for problematic tables

-- Ensure UUID generator is available
create extension if not exists pgcrypto;

-- Check if table is part of any publication and temporarily remove it
do $$
declare
    pub_name text;
    was_published boolean := false;
begin
    -- Find publications that include this table
    for pub_name in 
        select pubname 
        from pg_publication_tables 
        where tablename = 'config' and schemaname = 'public'
    loop
        was_published := true;
        execute format('alter publication %I drop table public.config', pub_name);
        raise notice 'Temporarily removed config table from publication %', pub_name;
    end loop;
    
    -- Store the publication state for restoration
    if was_published then
        perform set_config('migration.config_was_published', 'true', false);
    end if;
end $$;

-- Add the id column with default
alter table public.config add column if not exists id uuid default gen_random_uuid();

-- Update existing rows
update public.config
set id = coalesce(id, gen_random_uuid())
where id is null;

-- Make id NOT NULL
alter table public.config alter column id set not null;

-- Add primary key if it doesn't exist
do $$
begin
  if not exists (
    select 1
    from pg_constraint
    where conrelid = 'public.config'::regclass
      and contype = 'p'
  ) then
    alter table public.config add primary key (id);
  end if;
end $$;

-- Restore table to publications if it was previously published
do $$
declare
    pub_name text;
begin
    if current_setting('migration.config_was_published', true) = 'true' then
        -- Add back to all publications that typically include all tables
        for pub_name in 
            select pubname 
            from pg_publication 
            where puballtables = true
        loop
            execute format('alter publication %I add table public.config', pub_name);
            raise notice 'Restored config table to publication %', pub_name;
        end loop;
    end if;
end $$;
