/*
 * -------------------------------------------------------
 * Section: Video Generation
 * Tables and types for video generation functionality
 * -------------------------------------------------------
 */

-- Video generation models enum
create type public.video_generation_model as enum(
  'veo-3-fast',
  'veo-3-standard'
);

-- Video generation status enum  
create type public.video_generation_status as enum(
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled'
);

-- Video messages table (extending existing video_messages structure)
-- Add columns to track video generation
alter table if exists public.video_messages 
add column if not exists is_generating boolean default false,
add column if not exists is_error boolean default false,
add column if not exists video_generation_id text,
add column if not exists video_generation_model public.video_generation_model,
add column if not exists video_generation_status public.video_generation_status,
add column if not exists video_generation_prompt text,
add column if not exists generated_video_url text,
add column if not exists generation_error_message text,
add column if not exists generation_started_at timestamptz,
add column if not exists generation_completed_at timestamptz;

-- Video generation jobs table for tracking generation operations
create table if not exists public.video_generation_jobs (
  id uuid primary key default gen_random_uuid(),
  message_id uuid references public.video_messages(id) on delete cascade not null,
  account_id uuid references public.accounts(id) on delete cascade not null,
  user_id uuid references auth.users(id) on delete cascade not null,
  
  -- Generation details
  model public.video_generation_model not null default 'veo-3-fast',
  prompt text not null,
  improved_prompt text, -- AI-improved version of the prompt
  
  -- External service tracking
  external_operation_id text, -- Veo operation ID
  external_operation_name text, -- Veo operation name
  
  -- Status and results
  status public.video_generation_status not null default 'pending',
  error_message text,
  generated_video_url text,
  video_storage_path text, -- Supabase storage path
  
  -- Metadata
  generation_config jsonb default '{}', -- Store additional config like aspect ratio, etc.
  
  -- Timestamps
  created_at timestamptz default now() not null,
  updated_at timestamptz default now() not null,
  started_at timestamptz,
  completed_at timestamptz
);

-- Enable RLS on video_generation_jobs
alter table public.video_generation_jobs enable row level security;

-- RLS policies for video_generation_jobs
create policy "Users can view their own video generation jobs"
  on public.video_generation_jobs for select
  using (
    user_id = auth.uid() or
    public.has_role_on_account(account_id)
  );

create policy "Users can insert video generation jobs for their accounts"
  on public.video_generation_jobs for insert
  with check (
    user_id = auth.uid() and
    public.has_role_on_account(account_id)
  );

create policy "Users can update their own video generation jobs"
  on public.video_generation_jobs for update
  using (
    user_id = auth.uid() or
    public.has_role_on_account(account_id)
  );

-- Indexes for performance
create index if not exists idx_video_generation_jobs_message_id 
  on public.video_generation_jobs(message_id);
  
create index if not exists idx_video_generation_jobs_account_id 
  on public.video_generation_jobs(account_id);
  
create index if not exists idx_video_generation_jobs_user_id 
  on public.video_generation_jobs(user_id);
  
create index if not exists idx_video_generation_jobs_status 
  on public.video_generation_jobs(status);
  
create index if not exists idx_video_generation_jobs_external_operation_id 
  on public.video_generation_jobs(external_operation_id);

-- Trigger to update updated_at timestamp
create or replace function public.update_video_generation_jobs_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

create trigger update_video_generation_jobs_updated_at
  before update on public.video_generation_jobs
  for each row
  execute function public.update_video_generation_jobs_updated_at();

-- Function to get active video generation jobs for polling
create or replace function public.get_active_video_generation_jobs(target_account_id uuid)
returns table(
  id uuid,
  message_id uuid,
  external_operation_id text,
  external_operation_name text,
  status public.video_generation_status,
  created_at timestamptz
)
language plpgsql
security definer
set search_path = ''
as $$
begin
  -- Check if user has access to this account
  if not public.has_role_on_account(target_account_id) then
    raise exception 'Access denied';
  end if;

  return query
  select 
    vgj.id,
    vgj.message_id,
    vgj.external_operation_id,
    vgj.external_operation_name,
    vgj.status,
    vgj.created_at
  from public.video_generation_jobs vgj
  where vgj.account_id = target_account_id
    and vgj.status in ('pending', 'processing')
  order by vgj.created_at desc;
end;
$$;

-- Grant permissions
grant execute on function public.get_active_video_generation_jobs(uuid) to authenticated;
