import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = getSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('Upload request - user:', user?.id);
    console.log('Environment check:', {
      NODE_ENV: process.env.NODE_ENV,
      hasSupabaseKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      hasImgbbKey: !!process.env.IMGBB_API_KEY,
      supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL
    });
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const companyId = formData.get('companyId') as string;

    console.log('Upload request details:', {
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
      companyId: companyId
    });

    if (!file) {
      console.error('No file provided in upload request');
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!companyId) {
      console.error('No company ID provided in upload request');
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Verify user has access to the company
    console.log('Verifying user access to company:', companyId);
    const { data: membership, error: membershipError } = await supabase
      .from('accounts_memberships')
      .select('account_id')
      .eq('user_id', user.id)
      .eq('account_id', companyId)
      .single();

    if (membershipError || !membership) {
      console.error('User does not have access to company:', {
        userId: user.id,
        companyId,
        error: membershipError?.message,
        errorCode: membershipError?.code,
        errorDetails: membershipError?.details
      });

      // Also log all user memberships for debugging
      const { data: allMemberships } = await supabase
        .from('accounts_memberships')
        .select('account_id, user_id')
        .eq('user_id', user.id);

      console.log('User memberships:', allMemberships);

      return NextResponse.json(
        { error: 'Access denied: You do not have permission to upload to this account' },
        { status: 403 }
      );
    }

    console.log('User access verified for company:', companyId);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type:', file.type);
      return NextResponse.json(
        { error: 'Only image files are allowed' },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.error('File too large:', file.size);
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to storage based on environment
    // Use Supabase storage if we have the service role key (production/staging/preview)
    // Otherwise use ImgBB for development
    const hasSupabaseServiceKey = !!process.env.SUPABASE_SERVICE_ROLE_KEY;
    let uploadResult;

    try {
      if (hasSupabaseServiceKey) {
        // Use Supabase storage for production/staging/preview
        console.log('Using Supabase storage for upload');
        uploadResult = await uploadToSupabase(buffer, companyId, file.name);
      } else {
        // Use ImgBB for development
        console.log('Using ImgBB for upload');
        uploadResult = await uploadToImgBB(buffer);
      }
    } catch (uploadError) {
      console.error('Upload failed:', uploadError);
      return NextResponse.json(
        { error: `Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      url: uploadResult.url,
      path: uploadResult.path || uploadResult.url,
    });

  } catch (error) {
    console.error('Error in image upload API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Upload image to Supabase Storage (production environment)
 */
async function uploadToSupabase(imageBuffer: Buffer, companyId: string, fileName: string): Promise<{ url: string; path: string }> {
  try {
    const { createClient } = require('@supabase/supabase-js');

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required');
    }

    console.log('Creating Supabase client for upload with service role');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueId = crypto.randomUUID();
    const fileExtension = fileName.split('.').pop() || 'png';
    const newFileName = `${uniqueId}-${timestamp}.${fileExtension}`;
    const filePath = `${companyId}/references/${newFileName}`;

    console.log('Uploading to Supabase storage:', {
      bucket: 'generated',
      filePath: filePath,
      fileSize: imageBuffer.length,
      contentType: `image/${fileExtension}`
    });

    // Upload to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('generated')
      .upload(filePath, imageBuffer, {
        contentType: `image/${fileExtension}`,
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('Supabase upload error:', {
        message: uploadError.message,
        statusCode: uploadError.statusCode,
        error: uploadError.error,
        details: uploadError.details,
        hint: uploadError.hint,
        code: uploadError.code
      });
      throw new Error(`Supabase upload failed: ${uploadError.message}`);
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('generated')
      .getPublicUrl(filePath);

    return { url: publicUrl, path: filePath };

  } catch (error) {
    throw new Error(`Failed to upload to Supabase: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to ImgBB (development environment)
 */
async function uploadToImgBB(imageBuffer: Buffer): Promise<{ url: string }> {
  const imgbbApiKey = process.env.IMGBB_API_KEY;

  if (!imgbbApiKey) {
    throw new Error('IMGBB_API_KEY environment variable is required');
  }

  // Convert buffer to base64 string
  const base64Image = imageBuffer.toString('base64');

  // Create form data using URLSearchParams for Node.js compatibility
  const formData = new URLSearchParams();
  formData.append('image', base64Image);

  const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData,
  });

  if (!imgbbRes.ok) {
    const errorText = await imgbbRes.text();
    throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
  }

  const imgbbData = await imgbbRes.json();

  if (!imgbbData.success) {
    throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
  }

  return { url: imgbbData.data.url };
}
