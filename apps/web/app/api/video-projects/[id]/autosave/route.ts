import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@kit/supabase/server-client';
import { requireUser } from '@kit/supabase/require-user';

// POST /api/video-projects/[id]/autosave - Save editor state
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const user = await requireUser(supabase);
    const projectId = params.id;
    const body = await request.json();

    const { editorState, saveType = 'auto' } = body;

    if (!editorState) {
      return NextResponse.json(
        { error: 'editorState is required' },
        { status: 400 }
      );
    }

    const { data: autosave, error } = await supabase
      .from('video_project_autosaves')
      .insert({
        project_id: projectId,
        user_id: user.id,
        editor_state: editorState,
        save_type: saveType
      })
      .select()
      .single();

    if (error) {
      console.error('Error saving autosave:', error);
      return NextResponse.json(
        { error: 'Failed to save editor state' },
        { status: 500 }
      );
    }

    return NextResponse.json({ autosave }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/video-projects/[id]/autosave:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/video-projects/[id]/autosave - Get latest editor state
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const user = await requireUser(supabase);
    const projectId = params.id;

    const { data: autosave, error } = await supabase
      .from('video_project_autosaves')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching autosave:', error);
      return NextResponse.json(
        { error: 'Failed to fetch editor state' },
        { status: 500 }
      );
    }

    return NextResponse.json({ autosave });
  } catch (error) {
    console.error('Error in GET /api/video-projects/[id]/autosave:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/video-projects/[id]/autosave - Clear autosaves
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const user = await requireUser(supabase);
    const projectId = params.id;

    const { error } = await supabase
      .from('video_project_autosaves')
      .delete()
      .eq('project_id', projectId)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error clearing autosaves:', error);
      return NextResponse.json(
        { error: 'Failed to clear autosaves' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/video-projects/[id]/autosave:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}