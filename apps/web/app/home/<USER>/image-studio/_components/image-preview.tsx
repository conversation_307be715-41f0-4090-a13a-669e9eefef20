'use client';

import React from 'react';
import { Trans } from '@kit/ui/trans';
import { Button } from '@kit/ui/button';
import { UndoIcon } from 'lucide-react';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  image_path?: string | null;
  created_at: number;
}

interface ImagePreviewProps {
  messages?: Message[];
  onUndo?: () => void;
}

export function ImagePreview({ messages, onUndo }: ImagePreviewProps) {
  // Find the most recent message with an image
  const latestImageMessage = messages?.slice().reverse().find(msg => msg.image_path);
  const hasImage = latestImageMessage && latestImageMessage.image_path;
  
  // Find the second-to-last message with an image for undo functionality
  const messagesWithImages = messages?.filter(msg => msg.image_path) || [];
  const canUndo = messagesWithImages.length >= 2;
  return (
    <div className="w-full h-full flex flex-col">
      {/* Undo Button - only show when there's an image and can undo */}
      {hasImage && canUndo && onUndo && (
        <div className="p-4 border-b">
          <div className="flex items-center justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={onUndo}
              className="flex items-center gap-2"
            >
              <UndoIcon className="h-4 w-4" />
              <Trans i18nKey="imageStudio:undo" defaults="Undo" />
            </Button>
          </div>
        </div>
      )}
      
      {/* Grid background container - matching video studio pattern */}
      <div
        className="z-0 image-container relative flex-1
        bg-slate-100/90 dark:bg-gray-800
        bg-[linear-gradient(to_right,#80808015_1px,transparent_1px),linear-gradient(to_bottom,#80808015_1px,transparent_1px)] 
        dark:bg-[linear-gradient(to_right,#80808010_1px,transparent_1px),linear-gradient(to_bottom,#80808010_1px,transparent_1px)]
        bg-[size:16px_16px] 
        shadow-lg rounded-lg"
      >
        {/* Content wrapper with centering */}
        <div className="z-10 absolute inset-4 flex items-center justify-center">
          {hasImage ? (
            // Show generated image
            <div className="w-full h-full flex items-center justify-center">
              <img
                src={latestImageMessage.image_path!}
                alt="Generated image"
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                onError={(e) => {
                  console.error('Failed to load image:', latestImageMessage.image_path);
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
          ) : (
            // Show placeholder when no image
            <div className="text-center p-8">
              <div className="text-muted-foreground mb-4">
                <svg 
                  className="h-16 w-16 mx-auto mb-4 opacity-50" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={1.5} 
                    d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" 
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                <Trans i18nKey="imageStudio:noImageYet" defaults="No image yet" />
              </h3>
              <p className="text-sm text-muted-foreground max-w-sm">
                <Trans 
                  i18nKey="imageStudio:getStartedMessage" 
                  defaults="Enter a prompt in the chat to generate an image, or upload an image to start editing" 
                />
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
