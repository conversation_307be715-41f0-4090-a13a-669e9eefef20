'use client';

import { useState } from 'react';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Upload, Globe, Palette, Edit, FileText, ArrowRight } from 'lucide-react';
import { Account } from '~/types/accounts';
import { upload } from '@vercel/blob/client';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { normalizeCompanyWebsiteUrl } from '@kit/shared/utils';
import { toast } from '@kit/ui/sonner';

export const AddBrand = ({ account }: { account: Account }) => {
  const [websiteUrl, setWebsiteUrl] = useState(account?.website || '');
  console.log("account", account.website);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingWebsite, setIsLoadingWebsite] = useState(false);
  const [isLoadingPdf, setIsLoadingPdf] = useState(false);
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();

  const handleManualCreation = async () => {
    setIsLoading(true);
    try {
      
      // Create brand with empty structure for manual filling
      const brandId = crypto.randomUUID();
      
      await zero.mutate.company_brand.insert({
        id: brandId,
        values: {
          company_id: workspace.account.id,
          brand_name: workspace.account.name || 'My Brand',
          is_generating: false,
          created_at: Date.now(),
          updated_at: Date.now(),
          brand_profile: {
            mission: '',
            vision: '',
            tagline: '',
            brand_attributes: []
          },
          messaging_strategy: {
            voice: [],
            differentiators: [],
            style_guide_reference: ''
          },
          visual_identity: {
            colorPalette: {
              categories: [],
              usageGuidelines: ''
            },
            fonts: [],
            logos: [],
            photography: {
              style: '',
              artDirection: [],
              rules: {
                do: [],
                dont: []
              }
            }
          },
          product_catalog: [],
          prompt_library: []
        },
        isHTML: false,
        html: null,
        brand_document_blob_obj: null,
      });

    } catch (error) {
      // Error handling can be added here if needed
    } finally {
      setIsLoading(false);
    }
  };
  const handleUrlSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!websiteUrl.trim()) return;
    
    setIsLoadingWebsite(true);
    try {
      // Normalize website URL same as onboarding
      let normalizedUrl = '';
      try {
        normalizedUrl = normalizeCompanyWebsiteUrl(websiteUrl);
      } catch (err) {
        toast.error('Please enter a valid website URL (e.g., example.com or https://example.com)');
        return;
      }
      setWebsiteUrl(normalizedUrl);

      // Call the scrape-website API with all=true to get full content
      const response = await fetch('/api/scrape-website', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: normalizedUrl,
          all: true,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();

      // TODO: Process the scraped content to extract brand information
        // Insert into company_brand with HTML content
        zero.mutate.company_brand.insert({
          id: crypto.randomUUID(),
          values: {
            company_id: workspace.account.id,
            is_generating: true,
            error_generating: false,
            created_at: Date.now(),
            updated_at: Date.now(),
          },
          isHTML: true,
          html: result.text,
          brand_document_blob_obj: null,
        });

    } catch (error) {
      console.error('Error scraping website:', error);
    } finally {
      setIsLoadingWebsite(false);
    }
  };

  const handlePdfUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoadingPdf(true);
    try {
      // Upload the file using the Vercel blob client
      const blob = await upload(file.name, file, {
        access: 'public',
        handleUploadUrl: '/api/documents/upload',
      });

      // TODO: Now process the uploaded PDF and extract brand information
        const response = await fetch('/api/documents/process', {
          method: 'POST',
          body: JSON.stringify({
            blobUrl: blob.downloadUrl,
          }),
        });
        const result = await response.json();
        // Insert into company_brand with PDF blob
        zero.mutate.company_brand.insert({
          id: crypto.randomUUID(),
          values: {
            company_id: workspace.account.id,
            is_generating: true,
            error_generating: false,
            created_at: Date.now(),
            updated_at: Date.now(),
          },
          isHTML: true,
          html: result,
          brand_document_blob_obj: null,
        });

    } catch (error) {
      // Error handling can be added here if needed
    } finally {
      setIsLoadingPdf(false);
    }
  };


  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Choose how to upload your brand
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Get started by selecting one of the options below. We&apos;ll help you create a comprehensive brand guide that ensures consistent messaging and visual design across all your content.
          </p>
        </div>

        {/* Three Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Website Card */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-blue-600" />
                </div>
              </div>
              <CardTitle className="text-xl">From Website</CardTitle>
              <CardDescription>
                Automatically extract brand information from your website
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <ul className="space-y-2 text-sm text-gray-600 mb-6">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  AI-powered content analysis
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  Extracts colors, fonts, and messaging
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  Quick and automated
                </li>
              </ul>
              
              {/* Website URL Input */}
              <form onSubmit={handleUrlSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="website-url" className="text-sm font-medium">
                    Website URL
                  </Label>
                  <Input
                    id="website-url"
                    type="text"
                    inputMode="url"
                    autoComplete="url"
                    placeholder="example.com"
                    value={account.website || ''}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    disabled={isLoadingWebsite}
                    className="w-full"
                  />
                </div>
                <Button 
                  type="submit" 
                  disabled={!websiteUrl.trim() || isLoadingWebsite}
                  className="w-full group"
                >
                  {isLoadingWebsite ? 'Analyzing...' : 'Extract Brand'}
                  {!isLoadingWebsite && <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* PDF Card */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-green-600" />
                </div>
              </div>
              <CardTitle className="text-xl">From PDF</CardTitle>
              <CardDescription>
                Upload your brand guidelines or style guide document
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <ul className="space-y-2 text-sm text-gray-600 mb-6">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  Upload existing brand docs
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  Preserves your current guidelines
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  Smart content extraction
                </li>
              </ul>
              
              {/* PDF File Upload */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="pdf-upload" className="text-sm font-medium">
                    Brand Guidelines PDF
                  </Label>
                  <Input
                    id="pdf-upload"
                    type="file"
                    accept=".pdf"
                    onChange={handlePdfUpload}
                    disabled={isLoadingPdf}
                    className="w-full file:mr-4 file:py-1 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                  />
                </div>
                <p className="text-xs text-gray-500">
                  {isLoadingPdf ? 'Processing your PDF...' : 'We\'ll extract brand information from your PDF automatically'}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Manual Card */}
          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={handleManualCreation}>
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                  <Edit className="h-8 w-8 text-purple-600" />
                </div>
              </div>
              <CardTitle className="text-xl">Manual Creation</CardTitle>
              <CardDescription>
                Start from scratch and build your brand guide step by step
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <ul className="space-y-2 text-sm text-gray-600 mb-6">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                  Full creative control
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                  Build at your own pace
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                  Perfect for new brands
                </li>
              </ul>
              <Button 
                className="w-full group" 
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Start Building'}
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
