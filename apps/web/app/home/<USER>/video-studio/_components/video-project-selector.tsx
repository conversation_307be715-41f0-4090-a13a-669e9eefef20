'use client';

import { useState } from 'react';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import { ChevronDown, Plus, Video, Calendar } from 'lucide-react';

interface VideoProjectSelectorProps {
  currentProjectId: string | null;
  onProjectChange: (projectId: string) => void;
}

export function VideoProjectSelector({
  currentProjectId,
  onProjectChange,
}: VideoProjectSelectorProps) {
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');

  // Load all projects for this account
  const [projects] = useZeroQuery(
    zero?.query.video_projects
      .where('account_id', '=', workspace.account.id)
      .orderBy('updated_at', 'desc'),
    {
      ttl: '5m'
    }
  );

  const currentProject = projects?.find(p => p.id === currentProjectId);

  const handleCreateProject = async () => {
    if (!zero || !workspace.user?.id || !newProjectName.trim()) return;

    setIsCreating(true);
    try {
      const projectId = crypto.randomUUID();

      await (zero.mutate.video_projects as any).insert({
        id: projectId,
        account_id: workspace.account.id,
        user_id: workspace.user.id,
        name: newProjectName.trim(),
        description: newProjectDescription.trim(),
        aspect_ratio: '16:9',
        fps: 30,
        width: 1920,
        height: 1080,
        status: 'draft',
        is_template: false,
        created_at: Date.now(),
        updated_at: Date.now(),
        created_by: workspace.user.id,
        updated_by: workspace.user.id,
      });

      // Switch to the new project (this will also update user_cache)
      onProjectChange(projectId);

      // Reset form and close dialog
      setNewProjectName('');
      setNewProjectDescription('');
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error('Failed to create project:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const formatDate = (timestamp: number | undefined) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getProjectStatus = (project: any) => {
    switch (project.status) {
      case 'completed':
        return '✅';
      case 'rendering':
        return '⏳';
      case 'draft':
      default:
        return '📝';
    }
  };

  return (
    <div className="flex items-center gap-4 py-4 border-b bg-background">
      <div className="flex items-center gap-2">
        <Video className="h-5 w-5 text-muted-foreground" />
        <span className="text-sm font-medium">Project:</span>
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="justify-between min-w-[200px]">
            <span className="truncate">
              {currentProject?.name || 'Select Project'}
            </span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[300px]">
          {projects && projects.length > 0 ? (
            <>
              {projects.map((project) => (
                <DropdownMenuItem
                  key={project.id}
                  className="cursor-pointer p-3"
                  onClick={() => onProjectChange(project.id)}
                >
                  <div className="flex items-start gap-3 w-full">
                    <span className="text-lg">{getProjectStatus(project)}</span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{project.name}</span>
                        {project.id === currentProjectId && (
                          <span className="text-xs bg-primary text-primary-foreground px-1.5 py-0.5 rounded">
                            Current
                          </span>
                        )}
                      </div>
                      {project.description && (
                        <p className="text-xs text-muted-foreground mt-1 truncate">
                          {project.description}
                        </p>
                      )}
                      <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>Updated {formatDate(project.updated_at)}</span>
                        <span>•</span>
                        <span className="capitalize">{project.status}</span>
                      </div>
                    </div>
                  </div>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
            </>
          ) : (
            <>
              <DropdownMenuItem disabled>
                <span className="text-muted-foreground">No projects found</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}

          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <DropdownMenuItem
                className="cursor-pointer"
                onSelect={(e) => {
                  e.preventDefault();
                  setIsCreateDialogOpen(true);
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                Create New Project
              </DropdownMenuItem>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Create New Video Project</DialogTitle>
                <DialogDescription>
                  Create a new video project to organize your video editing work.
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="project-name">Project Name</Label>
                  <Input
                    id="project-name"
                    placeholder="Enter project name..."
                    value={newProjectName}
                    onChange={(e) => setNewProjectName(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="project-description">
                    Description (optional)
                  </Label>
                  <Textarea
                    id="project-description"
                    placeholder="Enter project description..."
                    value={newProjectDescription}
                    onChange={(e) => setNewProjectDescription(e.target.value)}
                    rows={3}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                  disabled={isCreating}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateProject}
                  disabled={!newProjectName.trim() || isCreating}
                >
                  {isCreating ? 'Creating...' : 'Create Project'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </DropdownMenuContent>
      </DropdownMenu>

      {currentProject && (
        <div className="text-sm text-muted-foreground">
          <span className="capitalize">{currentProject.status}</span>
          {currentProject.updated_at && (
            <span> • Updated {formatDate(currentProject.updated_at)}</span>
          )}
        </div>
      )}
    </div>
  );
}