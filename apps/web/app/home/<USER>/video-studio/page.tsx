'use client';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { TeamAccountLayoutPageHeader } from '../_components/layout';
import { VideoStudioWorkspace } from './_components/video-studio-workspace';


function VideoStudioPage() {
  const workspace = useTeamAccountWorkspace();

  return (
    <div className="">
      {/* <TeamAccountLayoutPageHeader
        account={workspace.account.slug}
        title={'Video Studio'}
        description={'Create and edit videos with our advanced video editor'}
      /> */}
      {/* <PageBody className=""> */}
        <div className="">
          <VideoStudioWorkspace />
        </div>
      {/* </PageBody> */}
    </div>
  );
}

export default (VideoStudioPage);

