"use client";

// UI Components
import { SimpleSidebar } from "./components/sidebar/simple-sidebar";
import { Editor } from "./components/core/editor";
import { SidebarProvider as EditorSidebarProvider } from "./contexts/sidebar-context";

// Context Providers
import { EditorProvider } from "./contexts/editor-context";

// Custom Hooks
import { useOverlays } from "./hooks/use-overlays";
import { useVideoPlayer } from "./hooks/use-video-player";
import { useTimelineClick } from "./hooks/use-timeline-click";
import { useAspectRatio } from "./hooks/use-aspect-ratio";
import { useCompositionDuration } from "./hooks/use-composition-duration";
import { useHistory } from "./hooks/use-history";

// Types
import { Overlay } from "./types";
import { useRendering } from "./hooks/use-rendering";
import {
  AUTO_SAVE_INTERVAL,
  DEFAULT_OVERLAYS,
  FPS,
  RENDER_TYPE,
} from "./constants";
import { TimelineProvider } from "./contexts/timeline-context";

// Autosave Components
import { AutosaveRecoveryDialog } from "./components/autosave/autosave-recovery-dialog";
import { AutosaveStatus } from "./components/autosave/autosave-status";
import { useState, useEffect } from "react";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { LocalMediaProvider } from "./contexts/local-media-context";
import { KeyframeProvider } from "./contexts/keyframe-context";
import { AssetLoadingProvider } from "./contexts/asset-loading-context";

export default function ReactVideoEditor({ projectId }: { projectId: string }) {
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();

  // Autosave state
  const [showRecoveryDialog, setShowRecoveryDialog] = useState(false);
  const [autosaveTimestamp, setAutosaveTimestamp] = useState<number | null>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState<number | null>(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);

  // Load project autosaves using Zero
  const [projectAutosaves] = useZeroQuery(
    zero?.query.video_project_autosaves
      .where("project_id", "=", projectId)
      // .where("user_id", "=", workspace.user?.id || "")
      .orderBy("created_at", "desc")
      .limit(1),
    {
      ttl: '5m'
    }
  );
  console.log("projectAutosaves", projectAutosaves);
  // Overlay management hooks
  const {
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    deleteOverlaysByRow,
    updateOverlayStyles,
    resetOverlays,
  } = useOverlays(DEFAULT_OVERLAYS);

  // Video player controls and state
  const { isPlaying, currentFrame, playerRef, togglePlayPause, formatTime } =
    useVideoPlayer();

  // Composition duration calculations
  const { durationInFrames, durationInSeconds } =
    useCompositionDuration(overlays);

  // Aspect ratio and player dimension management
  const {
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
  } = useAspectRatio();

  // Event handlers
  const handleOverlayChange = (updatedOverlay: Overlay) => {
    changeOverlay(updatedOverlay.id, () => updatedOverlay);
  };

  const { width: compositionWidth, height: compositionHeight } =
    getAspectRatioDimensions();

  const handleTimelineClick = useTimelineClick(playerRef, durationInFrames);

  const inputProps = {
    overlays,
    durationInFrames,
    fps: FPS,
    width: compositionWidth,
    height: compositionHeight,
    src: "",
  };

  const { renderMedia, state } = useRendering(
    "TestComponent",
    inputProps,
    RENDER_TYPE
  );

  // Replace history management code with hook
  const { undo, redo, canUndo, canRedo } = useHistory(overlays, setOverlays);

  // Create the editor state object to be saved
  const editorState = {
    overlays,
    aspectRatio,
    playerDimensions,
  };

  // Check for existing autosave on mount and auto-restore
  useEffect(() => {
    if (projectAutosaves && projectAutosaves.length > 0 && !initialLoadComplete) {
      const latestAutosave = projectAutosaves[0];
      const loadedState = latestAutosave.editor_state;
      
      console.log("Auto-restoring from autosave:", loadedState);
      
      if (loadedState) {
        // Automatically restore the saved state
        if (loadedState.overlays) {
          setOverlays([...loadedState.overlays]);
        }
        if (loadedState.aspectRatio) {
          setAspectRatio(loadedState.aspectRatio);
        }
        if (loadedState.playerDimensions) {
          updatePlayerDimensions(
            loadedState.playerDimensions.width,
            loadedState.playerDimensions.height
          );
        }
      }
      
      setAutosaveTimestamp(latestAutosave.created_at || Date.now());
      // Only show recovery dialog for manual saves or if you want user confirmation
      // setShowRecoveryDialog(true);
    }
    setInitialLoadComplete(true);
  }, [projectAutosaves, initialLoadComplete]);

  // Auto-save using Zero
  useEffect(() => {
    if (!zero || !projectId || !workspace.user?.id) return;

    const saveInterval = setInterval(async () => {
      try {
        setIsSaving(true);

        // Save autosave using Zero mutator
        await (zero.mutate.video_project_autosaves as any).insert({
          id: crypto.randomUUID(),
          project_id: projectId,
          user_id: workspace.user.id,
          editor_state: editorState,
          save_type: 'auto',
          created_at: Date.now(),
        });

        // Update project's updated_at timestamp
        await (zero.mutate.video_projects as any).update({
          id: projectId,
          updated_at: Date.now(),
          updated_by: workspace.user.id,
        });

        setLastSaveTime(Date.now());
      } catch (error) {
        console.error("Autosave failed:", error);
      } finally {
        setIsSaving(false);
      }
    }, AUTO_SAVE_INTERVAL);

    return () => clearInterval(saveInterval);
  }, [zero, projectId, workspace.user?.id, editorState]);

  // Functions for manual save/load
  const saveState = async () => {
    if (!zero || !workspace.user?.id) return false;

    try {
      setIsSaving(true);

      // Save autosave
      await (zero.mutate.video_project_autosaves as any).insert({
        id: crypto.randomUUID(),
        project_id: projectId,
        user_id: workspace.user.id,
        editor_state: editorState,
        save_type: 'manual',
        created_at: Date.now(),
      });

      // Update project timestamp
      await (zero.mutate.video_projects as any).update({
        id: projectId,
        updated_at: Date.now(),
        updated_by: workspace.user.id,
      });

      setLastSaveTime(Date.now());
      return true;
    } catch (error) {
      console.error("Manual save failed:", error);
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const loadState = async () => {
    if (!projectAutosaves || projectAutosaves.length === 0) return null;

    const latestAutosave = projectAutosaves[0];
    const loadedState = latestAutosave.editor_state;

    if (loadedState) {
      // Apply loaded state to editor
      setOverlays(loadedState.overlays || []);
      if (loadedState.aspectRatio) setAspectRatio(loadedState.aspectRatio);
      if (loadedState.playerDimensions)
        updatePlayerDimensions(
          loadedState.playerDimensions.width,
          loadedState.playerDimensions.height
        );
    }

    return loadedState;
  };

  // Mark initial load as complete after component mounts
  useEffect(() => {
    setInitialLoadComplete(true);
  }, []);

  console.log("DEBUG: overlays", overlays);
  // Handle recovery dialog actions
  const handleRecoverAutosave = async () => {
    const loadedState = await loadState();
    console.log("loadedState", loadedState);
    console.log("Recovered overlays:", loadedState?.overlays);
    
    // Force a re-render to ensure overlays are applied
    if (loadedState?.overlays) {
      setOverlays([...loadedState.overlays]);
    }
    
    setShowRecoveryDialog(false);
  };

  const handleDiscardAutosave = () => {
    setShowRecoveryDialog(false);
  };

  // Manual save function for use in keyboard shortcuts or save button
  const handleManualSave = async () => {
    setIsSaving(true);
    await saveState();
  };

  // Set up keyboard shortcut for manual save (Ctrl+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        handleManualSave();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [editorState]);

  // Combine all editor context values
  const editorContextValue = {
    // Overlay management
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    handleOverlayChange,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    resetOverlays,

    // Player controls
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    handleTimelineClick,
    playbackRate,
    setPlaybackRate,

    // Dimensions and duration
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    durationInFrames,
    durationInSeconds,

    // Add renderType to the context
    renderType: RENDER_TYPE,
    renderMedia,
    state,

    deleteOverlaysByRow,

    // History management
    undo,
    redo,
    canUndo,
    canRedo,

    // New style management
    updateOverlayStyles,

    // Autosave
    saveProject: handleManualSave,
  };

  return (
    <EditorSidebarProvider>
      <KeyframeProvider>
        <TimelineProvider>
          <EditorProvider value={editorContextValue}>
            <LocalMediaProvider>
              <AssetLoadingProvider>
                {/* Simple flex layout like Image Studio - no conflicting providers */}
                <div className="flex h-full w-full">
                  {/* Video Editor Tools Sidebar */}
                  <div className="w-[350px] shrink-0 border-r bg-background">
                    <SimpleSidebar />
                  </div>

                  {/* Main Editor Content */}
                  <div className="flex-1">
                    <Editor />
                  </div>
                </div>

                {/* Autosave Status Indicator */}
                <AutosaveStatus
                  isSaving={isSaving}
                  lastSaveTime={lastSaveTime}
                />

                {/* Autosave Recovery Dialog */}
                {showRecoveryDialog && autosaveTimestamp && (
                  <AutosaveRecoveryDialog
                    projectId={projectId}
                    timestamp={autosaveTimestamp}
                    onRecover={handleRecoverAutosave}
                    onDiscard={handleDiscardAutosave}
                    onClose={() => setShowRecoveryDialog(false)}
                  />
                )}
              </AssetLoadingProvider>
            </LocalMediaProvider>
          </EditorProvider>
        </TimelineProvider>
      </KeyframeProvider>
    </EditorSidebarProvider>
  );
}
