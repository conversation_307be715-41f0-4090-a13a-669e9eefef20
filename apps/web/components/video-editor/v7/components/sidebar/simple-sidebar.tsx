"use client";

import * as React from "react";
import {
  Film,
  Music,
  Type,
  Subtitles,
  ImageIcon,
  <PERSON>older<PERSON><PERSON>,
  <PERSON>er,
  Layout,
  MessageCircle,
} from "lucide-react";
import { useSidebar } from "../../contexts/sidebar-context";
import { VideoOverlayPanel } from "../overlays/video/video-overlay-panel";
import { TextOverlaysPanel } from "../overlays/text/text-overlays-panel";
import SoundsPanel from "../overlays/sounds/sounds-panel";
import { OverlayType } from "../../types";
import { CaptionsPanel } from "../overlays/captions/captions-panel";
import { ImageOverlayPanel } from "../overlays/images/image-overlay-panel";
import { LocalMediaPanel } from "../overlays/local-media/local-media-panel";
import { StickersPanel } from "../overlays/stickers/stickers-panel";
import { TemplateOverlayPanel } from "../overlays/templates/template-overlay-panel";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChatPanel } from "../overlays/chat/chat-panel";

// Simple sidebar without complex UI components
export function SimpleSidebar() {
  const { activePanel, setActivePanel } = useSidebar();

  const sidebarItems = [
    { type: OverlayType.CHAT, icon: MessageCircle, label: "Chat" },
    { type: OverlayType.TEXT, icon: Type, label: "Text" },
    { type: OverlayType.VIDEO, icon: Film, label: "Video" },
    { type: OverlayType.SOUND, icon: Music, label: "Audio" },
    { type: OverlayType.IMAGE, icon: ImageIcon, label: "Images" },
    { type: OverlayType.STICKER, icon: Sticker, label: "Stickers" },
    { type: OverlayType.CAPTION, icon: Subtitles, label: "Captions" },
    { type: OverlayType.LOCAL_DIR, icon: FolderOpen, label: "Local" },
    { type: OverlayType.TEMPLATE, icon: Layout, label: "Templates" },
  ];

  const renderActivePanel = () => {
    switch (activePanel) {
      case OverlayType.TEXT:
        return <TextOverlaysPanel />;
      case OverlayType.VIDEO:
        return <VideoOverlayPanel />;
      case OverlayType.SOUND:
        return <SoundsPanel />;
      case OverlayType.CAPTION:
        return <CaptionsPanel />;
      case OverlayType.IMAGE:
        return <ImageOverlayPanel />;
      case OverlayType.LOCAL_DIR:
        return <LocalMediaPanel />;
      case OverlayType.STICKER:
        return <StickersPanel />;
      case OverlayType.TEMPLATE:
        return <TemplateOverlayPanel />;
      case OverlayType.CHAT:
        return <ChatPanel />;
      default:
        return <ChatPanel />;
    }
  };

  return (
    <div className="flex h-full">
      {/* Sidebar Menu */}
      <div className="w-20 bg-muted border-r flex flex-col items-center py-4 space-y-3">
        {sidebarItems.map((item) => {
          console.log("item", item);
          const Icon = item.icon;
          return (
            <Button
              key={item.type}
              variant={activePanel === item.type ? "default" : "ghost"}
              className="w-16 h-14 p-1 flex flex-col items-center justify-center gap-1"
              onClick={() => setActivePanel(item.type)}
              title={item.label}
            >
              <Icon className="h-4 w-4" />
              <span className="text-[10px] leading-tight">{item.label}</span>
            </Button>
          );
        })}
      </div>

      {/* Active Panel Content */}
      <div className="flex-1 overflow-hidden">
        {renderActivePanel()}
      </div>
    </div>
  );
}