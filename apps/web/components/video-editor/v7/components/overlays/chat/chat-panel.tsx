'use client';

import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@kit/ui/card';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { VideoChatInput } from './video-chat-input';
import { ConversationDropdown } from './conversation-dropdown';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useEditorContext } from '../../../contexts/editor-context';
import { OverlayType, ClipOverlay } from '../../../types';

interface VideoMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  created_at: number;
  conversation_id: string;
  user_id: string;
  company_id: string;
  video_project_id?: string;
  is_generating?: boolean;
  is_error?: boolean;
  video_generation_id?: string;
  video_generation_model?: string;
  video_generation_status?: string;
  generated_video_url?: string;
}

interface VideoConversation {
  id: string;
  title?: string;
  user_id: string;
  company_id: string;
  video_project_id?: string;
  created_at: number;
  updated_at: number;
}

export function ChatPanel() {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  const { addOverlay, durationInFrames } = useEditorContext();
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [isNewConversation, setIsNewConversation] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('veo-3-fast');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Query for existing conversations for this company
  const [conversations] = useZeroQuery(
    zero.query.video_conversations
      .where("company_id", "=", workspace.account.id as string)
      .orderBy("updated_at", "desc"),
    {
      ttl: '10m'
    }
  );

  // Query for messages in the current conversation
  const [messages] = useZeroQuery(
    currentConversationId ? 
      zero.query.video_messages
        .where("conversation_id", "=", currentConversationId)
        .orderBy("created_at", "asc") :
      zero.query.video_messages.where("id", "=", "never-match"), // Empty query when no conversation
    { ttl: '10m' }
  );

  // Auto-select the most recent conversation if none is selected and not starting a new conversation
  useEffect(() => {
    if (!currentConversationId && !isNewConversation && conversations && conversations.length > 0) {
      setCurrentConversationId(conversations[0]?.id || null);
    }
  }, [conversations, currentConversationId, isNewConversation]);

  // Auto-scroll to bottom when messages change or when loading state changes
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  // Poll for video generation completion
  useEffect(() => {
    const activeGenerations = messages?.filter(msg => msg.is_generating) || [];
    
    if (activeGenerations.length === 0) return;

    const pollInterval = setInterval(async () => {
      try {
        // Check if any messages are still generating
        const stillGenerating = messages?.filter(msg => msg.is_generating) || [];
        if (stillGenerating.length === 0) {
          clearInterval(pollInterval);
          return;
        }

        console.log(`Polling for ${stillGenerating.length} active video generations...`);
        
        // In a real implementation, you would poll the backend for status updates
        // For now, we'll rely on the backend's background polling to update the database
        // and the Zero sync to update the UI automatically
        
      } catch (error) {
        console.error('Error polling video generation status:', error);
      }
    }, 10000); // Poll every 10 seconds

    return () => clearInterval(pollInterval);
  }, [messages]);

  // Video-only messages are now created on the backend when generation completes
  // This ensures proper Zero Sync handling and prevents race conditions

  // Function to add generated video to timeline
  const addVideoToTimeline = (videoUrl: string, videoTitle?: string) => {
    try {
      // Create a new video overlay
      const newVideoOverlay: ClipOverlay = {
        id: Date.now(), // Simple ID generation - in production use proper UUID
        type: OverlayType.VIDEO,
        content: videoTitle || 'Generated Video',
        src: videoUrl,
        durationInFrames: 240, // 8 seconds at 30fps - Veo 3 generates 8-second videos
        from: 0, // Start at beginning
        height: 400,
        width: 600,
        left: 0,
        top: 0,
        row: 1, // Place on first available row
        isDragging: false,
        rotation: 0,
        videoStartTime: 0,
        speed: 1,
        styles: {
          opacity: 1,
          zIndex: 1,
          objectFit: 'contain',
          volume: 1,
        },
      };

      // Add the overlay to the timeline
      addOverlay(newVideoOverlay);
      
      console.log('Video added to timeline:', videoUrl);
    } catch (error) {
      console.error('Error adding video to timeline:', error);
    }
  };

  const handleNewConversation = () => {
    setCurrentConversationId(null);
    setIsNewConversation(true);
  };

  const handleSelectConversation = (conversationId: string) => {
    setCurrentConversationId(conversationId);
    setIsNewConversation(false);
  };

  const handleConversationCreated = (conversationId: string) => {
    setCurrentConversationId(conversationId);
    setIsNewConversation(false);
  };

  const createNewConversation = async (): Promise<string> => {
    const conversationId = crypto.randomUUID();
    console.log("Creating new video conversation", conversationId);
    await (zero.mutate.video_conversations as any).insert({
      id: conversationId,
      values: {
        title: null,
        user_id: workspace.user.id,
        company_id: workspace.account.id,
        video_project_id: null, // TODO: Link to current video project if available
      }
    });

    return conversationId;
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    setIsLoading(true);

    try {
      // Create new conversation if this is the first message
      let conversationId = currentConversationId;
      if (!conversationId) {
        conversationId = await createNewConversation();
        handleConversationCreated(conversationId);
      }

      // Save user message
      const messageId = crypto.randomUUID();
      await (zero.mutate.video_messages as any).insert({
        id: messageId,
        values: {
          content: content.trim(),
          role: 'user',
          user_id: workspace.user.id,
          company_id: workspace.account.id,
          conversation_id: conversationId,
          video_project_id: null, // TODO: Link to current video project if available
        }
      });

      // Get AI response from video chat API
      const chatMessages = [
        ...(messages || []).map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        })),
        {
          role: 'user' as const,
          content: content.trim(),
        }
      ];

      const response = await fetch('/api/ai/video-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: chatMessages,
          userId: workspace.user.id,
          companyId: workspace.account.id,
          conversationId,
          messageId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const aiResponse = await response.json();

      // Save AI response
      const aiMessageId = crypto.randomUUID();
      await (zero.mutate.video_messages as any).insert({
        id: aiMessageId,
        values: {
          content: aiResponse.content || "I'm here to help you create amazing videos! Please describe what kind of video you'd like to generate.",
          role: 'assistant',
          user_id: workspace.user.id,
          company_id: workspace.account.id,
          conversation_id: conversationId,
          video_project_id: null,
        }
      });

      setIsLoading(false);

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Save error message
      if (currentConversationId) {
        const errorMessageId = crypto.randomUUID();
        await (zero.mutate.video_messages as any).insert({
          id: errorMessageId,
          values: {
            content: "I apologize, but I encountered an error. Please try again.",
            role: 'assistant',
            user_id: workspace.user.id,
            company_id: workspace.account.id,
            conversation_id: currentConversationId,
            video_project_id: null,
          }
        }).catch(console.error);
      }
      
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col" style={{ height: 'calc(100vh - 120px)' }}>
      <Card className="flex flex-col overflow-hidden" style={{ height: '100%' }}>
        {/* Header with Conversation Dropdown and Model Selector */}
        <CardHeader className="flex-shrink-0 pb-3">
          <div className="flex justify-between items-center mb-3">
            <CardTitle className="text-lg">
              <Trans i18nKey="videoEditor:chatTitle" defaults="Video Chat" />
            </CardTitle>
            <ConversationDropdown
              conversations={(conversations || []) as any[]}
              currentConversationId={currentConversationId}
              onSelectConversation={handleSelectConversation}
              onNewConversation={handleNewConversation}
            />
          </div>
          
          {/* Model Selection */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Model:</span>
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-48 h-8">
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="veo-3-fast">
                  <div className="flex items-center gap-2">
                    <span>Veo 3 Fast</span>
                    <Badge variant="secondary" className="text-xs">with sound</Badge>
                  </div>
                </SelectItem>
                <SelectItem value="veo-3-standard">
                  <div className="flex items-center gap-2">
                    <span>Veo 3 Standard</span>
                    <Badge variant="secondary" className="text-xs">with sound</Badge>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col p-0 min-h-0">
          {/* Messages area - this will scroll independently */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full px-4">
              {!messages || messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-center p-6">
                  <div>
                    <div className="text-muted-foreground mb-4">
                      <svg 
                        className="h-12 w-12 mx-auto mb-4 opacity-50" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        <path 
                          strokeLinecap="round" 
                          strokeLinejoin="round" 
                          strokeWidth={1.5} 
                          d="M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 0 1-2.555-.337A5.972 5.972 0 0 1 5.41 20.97a5.969 5.969 0 0 1-.474-.065 4.48 4.48 0 0 0 .978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25Z" 
                        />
                      </svg>
                    </div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">
                      <Trans i18nKey="videoEditor:startVideoChat" defaults="Start a video conversation" />
                    </h3>
                    <p className="text-xs text-muted-foreground max-w-xs">
                      <Trans 
                        i18nKey="videoEditor:videoChatDescription" 
                        defaults="Ask me to generate videos using AI, or help with editing, effects, and transitions" 
                      />
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4 py-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] rounded-lg p-3 ${
                          message.role === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted text-muted-foreground'
                        }`}
                      >
                        {/* Only show content if it's not empty (for video-only messages) */}
                        {message.content && (
                          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        )}
                        
                        {/* Video Generation Status */}
                        {message.is_generating && (
                          <div className="mt-2 flex items-center gap-2">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce" />
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                            </div>
                            <span className="text-xs opacity-70">
                              <Trans i18nKey="videoEditor:generatingVideo" defaults="Generating video..." />
                            </span>
                          </div>
                        )}
                        
                        {/* Video Generation Error */}
                        {message.is_error && (
                          <div className="mt-2">
                            <Badge variant="destructive" className="text-xs">
                              <Trans i18nKey="videoEditor:generationError" defaults="Generation failed" />
                            </Badge>
                          </div>
                        )}
                        
                        {/* Generated Video - only show if this is a video-only message (empty content) or if no video-only message exists yet */}
                        {message.generated_video_url && (message.content === '' || !messages?.some(msg => 
                          msg.role === 'user' && 
                          msg.content === '' && 
                          msg.generated_video_url === message.generated_video_url
                        )) && (
                          <div className="mt-2">
                            <video
                              src={message.generated_video_url}
                              controls
                              className="w-full max-w-sm rounded border"
                              preload="metadata"
                            >
                              Your browser does not support the video tag.
                            </video>
                            <div className="mt-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-xs h-7 bg-primary text-primary-foreground "
                                onClick={() => {
                                  if (message.generated_video_url) {
                                    addVideoToTimeline(
                                      message.generated_video_url,
                                      `Generated Video`
                                    );
                                  }
                                }}
                              >
                                <Trans i18nKey="videoEditor:addToTimeline" defaults="Add to Timeline" />
                              </Button>
                            </div>
                          </div>
                        )}
                        
                        <p className="text-xs opacity-70 mt-1">
                          {new Date(message.created_at || Date.now()).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-muted text-muted-foreground rounded-lg p-3">
                        <div className="flex items-center space-x-2">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-current rounded-full animate-bounce" />
                            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                          </div>
                          <span className="text-xs">
                            <Trans i18nKey="videoEditor:thinking" defaults="Thinking..." />
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Invisible element to scroll to */}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Input area - fixed at bottom */}
          <div className="flex-shrink-0 border-t p-4">
            <VideoChatInput 
              onSubmit={handleSendMessage} 
              disabled={isLoading}
              placeholder="Describe the video you want to generate..."
              companyId={workspace.account.id}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
