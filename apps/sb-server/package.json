{"name": "@kit/sb-server", "version": "1.0.0", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "build:deps": "pnpm --filter @kit/zero-schema build", "build:all": "pnpm run build:deps && pnpm run build", "setup-zero-schema": "./scripts/setup-zero-schema.sh", "prebuild": "pnpm run setup-zero-schema", "start": "node dist/index.js", "dev": "nodemon --exec \"node --import tsx\" src/index.ts", "deploy:preview": "./deploy-preview-nodejs.sh", "test": "jest", "test:watch": "jest --watch", "test:gora": "tsx src/scripts/test-gora-icp.ts", "test:content-schedule": "tsx src/scripts/test-gora-schedule.ts", "test:reddit": "tsx src/scripts/test-reddit-scraper.ts", "test:tiktok": "tsx src/scripts/test-tiktok-scraper.ts", "test:twitter": "tsx src/scripts/test-twitter-scraper.ts", "debug:twitter": "tsx src/scripts/debug-twitter-structure.ts"}, "engines": {"node": ">=18.0.0"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@blocknote/server-util": "0.33.0", "@google/genai": "^1.4.0", "@openai/agents": "^0.1.2", "@rocicorp/zero": "0.21.**********", "@supabase/supabase-js": "2.49.4", "@types/body-parser": "^1.19.6", "apify-client": "^2.12.6", "axios": "^1.9.0", "body-parser": "^2.2.0", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "google-auth-library": "^10.3.0", "jigsawstack": "^0.2.8", "langfuse": "^3.37.6", "openai": "^5.1.1", "postgres": "^3.4.7", "short-unique-id": "^5.3.2", "uuid": "^13.0.0", "zod": "^3.24.4"}, "devDependencies": {"@types/dotenv": "^6.1.1", "@types/express": "^5.0.2", "@types/jest": "^29.5.12", "@types/node": "^22.15.9", "esbuild": "^0.25.9", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.8.3"}}