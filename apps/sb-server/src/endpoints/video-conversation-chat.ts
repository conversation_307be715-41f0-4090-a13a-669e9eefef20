import { Request, Response } from 'express';
import { run } from '@openai/agents';
import { streamVideoConversationChat, ChatMessage } from '../lib/utils/videoConversationAgent.js';

interface VideoConversationChatRequest {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
}

export async function videoConversationChatHandler(req: Request, res: Response): Promise<void> {
  try {
    const { messages, userId, companyId, conversationId, messageId }: VideoConversationChatRequest = req.body;

    // Validate required fields
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      res.status(400).json({ 
        error: 'Messages array is required and cannot be empty' 
      });
      return;
    }

    if (!userId || !companyId || !conversationId) {
      res.status(400).json({ 
        error: 'userId, companyId, and conversationId are required' 
      });
      return;
    }

    // Validate message structure
    for (const msg of messages) {
      if (!msg.role || !msg.content) {
        res.status(400).json({ 
          error: 'Each message must have role and content' 
        });
        return;
      }
      if (!['user', 'assistant'].includes(msg.role)) {
        res.status(400).json({ 
          error: 'Message role must be either "user" or "assistant"' 
        });
        return;
      }
    }

    // Ensure the latest message is from the user
    const latestMessage = messages[messages.length - 1];
    if (latestMessage.role !== 'user') {
      res.status(400).json({ 
        error: 'Latest message must be from user' 
      });
      return;
    }

    console.log(`Processing video conversation chat for user ${userId}, company ${companyId}`);

    // Create context for the video conversation agent
    const context = {
      userId,
      companyId,
      conversationId,
      messageId,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        created_at: Date.now(),
      })),
    };

    // Get the agent and conversation details
    const { agent, userMessage } = await streamVideoConversationChat({
      messages,
      context,
    });

    console.log(`Starting video conversation with message: "${userMessage}"`);

    // Run the agent with the user's message
    const result = await run(agent, userMessage, {
      context: context,
    });

    // Get the response
    let responseContent = result.finalOutput || '';

    if (!responseContent) {
      responseContent = "I'm here to help you create amazing videos! Please describe what kind of video you'd like to generate, and I'll help you create the perfect prompt for Veo 3.";
    }

    console.log(`Video conversation response generated: ${responseContent.substring(0, 100)}...`);

    // Return the response
    res.json({
      content: responseContent,
      role: 'assistant',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in video conversation chat:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const statusCode = errorMessage.includes('validation') || errorMessage.includes('required') ? 400 : 500;
    
    res.status(statusCode).json({
      error: 'Failed to process video conversation',
      message: errorMessage,
      timestamp: new Date().toISOString(),
    });
  }
}
