import dotenv from "dotenv";
import { observeOpenAI } from "langfuse";
import OpenAI from "openai";

dotenv.config();

export const LLM_MODEL_ID = "google/gemini-2.5-pro";

export const callLLM = async (
  prompt: any,
  langfuseConfig: any,
  model: string,
  additionalParams: any = {
    parse: true,
  },
  additionalMessages: any = []
) => {
  try {
    const client = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: process.env.OPENROUTER_API_KEY,
      dangerouslyAllowBrowser: true, // Required for server-side usage in some environments
    });


    const langfuseClient = observeOpenAI(client);

    const selectedModel = langfuseConfig?.llm_model_id || model || LLM_MODEL_ID;


    let messages = [];
    
    if (prompt && prompt.trim && prompt.trim().length > 0) {
      messages.push({ role: "user", content: prompt });
    }
    
    messages = [...messages, ...additionalMessages];

    const apiParams: any = {
      model: selectedModel,
      temperature: langfuseConfig.temperature,
      messages: messages,
      ...additionalParams,
    };

    if (langfuseConfig.response_format && typeof langfuseConfig.response_format === "object") {
      apiParams.response_format = langfuseConfig.response_format;
    } else if (langfuseConfig.schema && typeof langfuseConfig.schema === "object") {
      apiParams.response_format = langfuseConfig.schema;
    }



    const response = await langfuseClient.chat.completions.create(apiParams);

    const messageContent = response.choices[0].message.content;
    const cleanedJson = clean(messageContent || "");
    if (additionalParams.parse) {
      try {
        return JSON.parse(cleanedJson);
      } catch (parseError) {
        console.error("JSON parse error:", parseError);
        console.error("Content that failed to parse:", cleanedJson);
        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
      }
    }
    return cleanedJson;
  } catch (error) {
    console.error({ error });
    throw new Error(`Failed to call LLM: ${error}`);
  }
};

const clean = (text: string) => text.trim().replace(/^```(?:\w*\n)?|```$/g, "");
