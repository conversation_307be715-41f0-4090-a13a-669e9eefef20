import { Agent, tool } from '@openai/agents';
import { z } from 'zod';
import { generateVideo } from '../services/generate-video.js';
import dotenv from 'dotenv';

dotenv.config();

interface VideoConversationContext {
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at: number;
  }>;
}

// Video generation models enum
type VideoGenerationModel = 'veo-3-fast' | 'veo-3-standard';

// Create the video generation tool
const generateVideoTool = tool({
  name: 'generate_video',
  description: 'Generate a video based on a detailed prompt using Veo 3. Call this when you have a complete, detailed prompt ready for video generation.',

  parameters: z.object({
    video_prompt: z.string().describe('The detailed video generation prompt with specific descriptions of action, style, camera movements, and audio cues'),
    model: z.enum(['veo-3-fast', 'veo-3-standard']).default('veo-3-fast').describe('Video generation model to use'),
    aspect_ratio: z.string().default('16:9').describe('Video aspect ratio (16:9 or 9:16)'),
    resolution: z.string().default('720p').describe('Video resolution (720p or 1080p for 16:9)'),
    style_notes: z.string().nullable().describe('Additional style, mood, or technical notes'),
    audio_cues: z.string().nullable().describe('Specific audio instructions like dialogue, sound effects, or ambient noise')
  }),
  async execute({ video_prompt, model, aspect_ratio, resolution, style_notes, audio_cues }, runContext) {
    try {
      console.log('Generating video with prompt:', video_prompt);
      
      // Get context from the run context
      const context = runContext?.context as VideoConversationContext;
      if (!context) {
        throw new Error('Context not available');
      }

      // Enhance the prompt with audio cues if provided
      let enhancedPrompt = video_prompt;
      if (audio_cues) {
        enhancedPrompt += ` ${audio_cues}`;
      }
      if (style_notes) {
        enhancedPrompt += ` ${style_notes}`;
      }

      // Generate the video using the video service
      const result = await generateVideo({
        video_prompt: enhancedPrompt,
        model: model as VideoGenerationModel,
        aspect_ratio,
        resolution,
        company_id: context.companyId,
        user_id: context.userId,
        message_id: context.messageId,
        conversation_id: context.conversationId,
      });

      console.log('Video generation started:', result.operationId);

      return `Video generation started successfully! 

**Generation Details:**
- Model: ${model}
- Resolution: ${resolution}
- Aspect Ratio: ${aspect_ratio}
- Operation ID: ${result.operationId}

**Your Prompt:** "${video_prompt}"
${audio_cues ? `**Audio Cues:** ${audio_cues}` : ''}
${style_notes ? `**Style Notes:** ${style_notes}` : ''}

The video is now being generated using Veo 3. This typically takes 1-6 minutes depending on complexity and server load. I'll let you know when it's ready!

You can continue chatting while the video generates, or ask me to create additional videos with different prompts.`;
    } catch (error) {
      console.error('Error generating video:', error);
      return `I apologize, but I encountered an error while starting the video generation: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again or let me know if you'd like to adjust the prompt.`;
    }
  },
});

export async function createVideoConversationAgent(context: VideoConversationContext) {
  const systemInstructions = `You are an AI assistant for a Video Editor application with Veo 3 video generation capabilities. You help users create detailed prompts for high-quality video generation with native audio.

Your workflow:
1. If the user's request is too short or lacks detail, ask clarifying questions to help create a comprehensive prompt
2. Once you have enough information to create a detailed, specific prompt, use the generate_video tool to create the video
3. Always call the generate_video tool when you have a complete prompt ready - don't just provide the prompt text

Guidelines for excellent Veo 3 prompts:
**Essential Elements:**
- Subject: What is the main focus (person, animal, object, scene)
- Action: What is happening (walking, dancing, flying, etc.)
- Setting/Environment: Where does this take place
- Camera work: Describe camera movements (dolly shot, aerial view, close-up, etc.)
- Style: Specify visual style (cinematic, documentary, animated, etc.)

**Audio Guidelines (Veo 3 has native audio):**
- Dialogue: Use quotes for speech ("Hello there," she said)
- Sound Effects: Describe specific sounds (engine roaring, birds chirping, footsteps on gravel)
- Ambient Audio: Describe the soundscape (gentle rain, bustling city, quiet forest)

**Technical Considerations:**
- Be specific about lighting (golden hour, neon lights, soft natural light)
- Include composition details (wide shot, extreme close-up, two-shot)
- Mention visual effects or atmosphere (misty, vibrant colors, dramatic shadows)
- For negative prompts: mention what to avoid (low quality, blurry, distorted)

**Examples of good prompts:**
- "A close-up cinematic shot of a woman walking along a beach at sunset, looking content. The camera follows her with a gentle dolly movement. Soft waves crash nearby, seagulls call in the distance."
- "Aerial drone view of a red convertible driving along a winding coastal road. The engine roars as it accelerates around curves. Dramatic cliffs and crashing waves below."

Context:
- User ID: ${context.userId}
- Company ID: ${context.companyId}
- Conversation ID: ${context.conversationId}
${context.messageId ? `- Message ID: ${context.messageId}` : ''}

Previous conversation:
${context.messages.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Remember: When you have enough information, use the generate_video tool to create the video. Always aim for cinematic quality with rich audio descriptions.`;

  const agent = new Agent({
    name: 'Video Studio Assistant',
    instructions: systemInstructions,
    tools: [generateVideoTool],
  });

  return agent;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface StreamChatParams {
  messages: ChatMessage[];
  context: VideoConversationContext;
}

export async function streamVideoConversationChat(params: StreamChatParams) {
  const { messages, context } = params;
  
  try {
    const agent = await createVideoConversationAgent(context);
    
    // Convert messages to the format expected by the agent
    const conversationHistory = messages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
    }));

    // Get the latest user message
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage || latestMessage.role !== 'user') {
      throw new Error('Latest message must be from user');
    }

    return {
      agent,
      userMessage: latestMessage.content,
      conversationHistory,
    };
  } catch (error) {
    console.error('Error creating video conversation agent:', error);
    throw new Error('Failed to create video conversation agent. Please try again.');
  }
}
