import { createMutators as createClientMutators } from './zero-schema/mutator.js';
import postgres from 'postgres';
import { scrapeWebsite } from './services/scrape-website.js';
import { generateICP } from './services/generate-icp.js';
import { generateMarketResearch } from './services/generate-market-research.js';
import crypto from 'crypto';
import { generatePersona } from './services/generate-persona.js';
import { scrapeWebsiteByElement } from './services/scrape-website-by-element.js';
import { companyCampaignsInsert } from './asyncTasks/company-campaigns-insert.js';
import { generateEngagementReport } from './services/generate-engagement-report.js';
import { scrapeLinkedInProfile } from './utils/scrape-linkedin-profiles.js';
import { companyBrandInsert } from './asyncTasks/company-brand-insert.js';
import { generateICPLinkedIn } from './services/generate-icp-linkedin.js';
import { ICPInsert } from './types/icp-insert.js';
import { icpWithAiInsert } from './asyncTasks/icp-with-ai-insert.js';
import { siteResearchInsert } from './asyncTasks/site-research-insert.js';
import { socialResearchInsert } from './asyncTasks/social-research-insert.js';
import { generateCampaignName } from './services/generate-campaign-name.js';
import { generateTaskContentAsync } from './asyncTasks/generate-task-content.js';

// Generate slug from campaign name (similar to campaign.ts)
function generateSlug(name: string): string {
  const generateUniqueId = (length = 10): string => {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const safeName = name || 'untitled';
  return `${safeName
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-') // Replace any non-alphanumeric char with hyphen
    .replace(/^-+|-+$/g, '')}-${generateUniqueId()}`; // Remove leading/trailing hyphens
}

const sql = postgres(process.env.ZERO_UPSTREAM_DB! as string);


export type AuthData = {
  // The logged-in user.
  sub: string;
};

export function createServerMutators(
  authData: AuthData,
  asyncTasks: Array<() => Promise<void>>,
) {
  
  const clientMutators = createClientMutators();
  console.log('clientMutators', clientMutators);
  return {
    // Reuse all client mutators as-is
    ...clientMutators,
    site_research: {
      insert: async (tx, { 
        id, 
        values
      }: {
        id: string;
        values: any;  
      }) => {
        await tx.mutate.site_research.insert({
          id,
          ...values,
        });
        asyncTasks.push(async () => {
          await siteResearchInsert(sql, {
            id,
            company_id: values.company_id,
            icps: values.icps,
            personal: values.personal,
            urls: values.urls,
            instruction: values.instruction,
            schema: values.schema,
            enable_web_search: values.enable_web_search,
            agent_mode: values.agent_mode
          });
        });
      },
    },
    accounts: {
      ...clientMutators.accounts,
      insert: async (tx: any, { id, name, primary_owner_user_id, is_personal_account, website }: {
        id: string;
        name: string;
        primary_owner_user_id: string;
        is_personal_account: boolean;
        website: string;
      }) => {
        console.log('🚀 Server mutator called with ID:', id, name, primary_owner_user_id, is_personal_account);

        await tx.mutate.accounts.insert({
          id, // Use the provided ID from client
          name,
          primary_owner_user_id,
          is_personal_account,
          website,
        });

        if (!is_personal_account) {
          await tx.mutate.accounts_memberships.insert({
            user_id: primary_owner_user_id,
            account_id: id,
            account_role: 'owner',
            created_at: Date.now(),
            updated_at: Date.now(),
          });
        }

        // ✅ Queue async work to be done AFTER the transaction commits
        asyncTasks.push(async () => {

          try {
            // Step 1 - Scrape the website
            console.log('Starting website scraping for:', name);
            const scrapedText = await scrapeWebsite(website);
            console.log('scrapedText', scrapedText);
            // Update account with scraped data
            await sql`
              INSERT INTO product_documents (id, company_id, content, created_at, file_path, file_type, title)
              VALUES (
                gen_random_uuid(),
                ${id},
                ${scrapedText.content},
                ${Date.now()},
                ${'Website'},
                ${'Website'},
                ${'Homepage Content'}
              )
            `;

            console.log('✅ Website scraped and account updated for:', name);

            // Step 2 - Generate ICP using Claude AI
            console.log('Starting ICP generation for:', name);

            const icpID = crypto.randomUUID();
           
            //insert a row to show we are generating the ICP
            await sql`
              INSERT INTO icps (id, created_at, company_id, is_generating)
              VALUES (
                ${icpID},
                ${Date.now()},
                ${id},
                true
              )
            `;

            const icpData = await generateICP(scrapedText.content, name, [], '');
            console.log('icpData', icpData);
            // Update the generated ICP into the icps table
            await sql`
              UPDATE icps 
              SET 
                data = ${sql.json(icpData as any)},
                name = ${icpData.name},
                is_generating = false,
                updated_at = ${Date.now()}
              WHERE id = ${icpID}
            `;

            //generate a persona

            const persona = await generatePersona(JSON.stringify(icpData));
            console.log({persona});
            await sql`
            INSERT INTO personas (id, icp_id, company_id, created_at, is_generating, data)
            VALUES (
              gen_random_uuid(),
              ${icpID},
              ${id},
              ${Date.now()},
              false,
              ${sql.json(persona)}
            )
          `;
            console.log('✅ ICP generated and inserted for:', name);

          } catch (error) {
            console.error('Failed to process account in async task:', error);

          }
          console.log('✅ Async task completed for account:', name, 'with ID:', id);
        });
      },
    },
    company_brand: {
      ...clientMutators.company_brand,
      insert: async (tx: any, { 
        id, 
        values,
        isHTML,
        html,
        brand_document_blob_obj
      }: {
        id: string;
        values: any;
        isHTML: boolean;
        html: string | null;
        brand_document_blob_obj: {
          url: string;
          pathname: string;
          downloadUrl: string;
        } | null;
      }) => {
        await clientMutators.company_brand.insert(tx, {
          id,
          values,
          isHTML,
          html,
          brand_document_blob_obj
        });
        if(isHTML || brand_document_blob_obj) {
        asyncTasks.push(async () => {
          await companyBrandInsert(sql, {
            id,
            values,
            isHTML,
            html,
            brand_document: brand_document_blob_obj
          });
        });
      }
      },
    },
    company_campaigns: {
      ...clientMutators.company_campaigns,
      insert: async (tx: any, { 
        id, 
        company_id, 
        user_id, 
        purpose, 
        objective, 
        start_date, 
        end_date, 
        templateId,
        external_research,
        products,
        target_icps,
        target_personas,
        channels,
        posts_per_week,
        color_tag
      }: {
        id: string;
        company_id: string;
        user_id: string;
        purpose: string;
        objective: string;
        start_date: string;
        end_date: string;
        templateId: string;
        external_research?: string[];
        products?: string[];
        target_icps?: string[];
        target_personas?: string[];
        channels?: string[];
        posts_per_week?: number;
        color_tag?: string;
      }) => {
        console.log('🚀 Server campaign mutator called with ID:', id);
        console.log('🚀 Server campaign mutator called with COMPANYID:', company_id);
        console.log('🚀 Server campaign mutator called with USERID:', user_id, purpose,
          objective,
          start_date,
          end_date,
          templateId,
          external_research,
          products,);
        console.log('🚀 icps:, personas:', {target_icps, target_personas});
        console.log('🚀 channels, posts_per_week, color_tag:', {channels, posts_per_week, color_tag});
        // Call the client mutator to insert the initial record
        await clientMutators.company_campaigns.insert(tx, {
          id,
          company_id,
          user_id,
          purpose,
          objective,
          start_date,
          end_date,
          templateId,
          external_research,
          products,
          target_icps,
          target_personas,
          channels,
          posts_per_week,
          color_tag
        });
        console.log('🚀 Server campaign mutator called with ID:', id, );
        
        console.log('🚀 Server campaign mutator reached here with ID:', id);
        // ✅ Queue async work to be done AFTER the transaction commits
        asyncTasks.push(async () => {
          try {
            // Generate campaign name using LLM
            const campaignInformation = `Purpose: ${purpose}. Objective: ${objective}`;
            const generatedName = await generateCampaignName(campaignInformation);
            
            // Update the campaign with the generated name
            await sql`
              UPDATE company_campaigns 
              SET 
                name = ${generatedName},
                slug = ${generateSlug(generatedName)}
              WHERE id = ${id}
            `;
            
            console.log('✅ Campaign name generated and updated:', generatedName);
          } catch (error) {
            console.error('Failed to generate campaign name:', error);
            // Keep the original purpose as name if generation fails
          }

          // Phase 1: Complete task creation first
          console.log('🚀 Phase 1: Creating campaign tasks and schedule...');
          await companyCampaignsInsert(sql, {
            id,
            company_id,
            objective,
            start_date,
            end_date,
            templateId,
            external_research,
            products,
            target_icps,
            target_personas,
            channels,
            posts_per_week
          });
          console.log('✅ Phase 1 completed: All tasks created in database');

          // Phase 2: Generate content for individual tasks after ALL tasks are created
          console.log('🚀 Phase 2: Starting content generation for tasks...');
          try {
            // Add a small delay to ensure database consistency
            await new Promise(resolve => setTimeout(resolve, 1000));

            await generateTaskContentAsync(sql, {
              campaignId: id,
              companyId: company_id,
            });
            console.log('✅ Phase 2 completed: Task content generation finished for campaign:', id);
          } catch (error) {
            console.error('❌ Phase 2 failed: Task content generation error for campaign:', id, error);
            // Don't throw here - we want the campaign creation to succeed even if content generation fails
          }
        });
      },
      update: async (tx: any, { 
        id,
        values,
        regenerate = false
      }: {
        id: string;
        values: any;
        regenerate?: boolean;
      }) => { 
        console.log('🚀 Server campaign update mutator called with ID:', id);
        console.log('🚀 Server campaign update mutator called with values:', values);
        if(regenerate) {
          await companyCampaignsInsert(sql, {...values})
        }
      },
    },
    generated_research: {
      ...clientMutators.generated_research,
      insert: async (tx: any, { 
        id, 
        account_id, 
        icp_id, 
        persona_id, 
        research_type, 
        time_filter, 
        title, 
        topic,
        created_by 
      }: {
        id: string;
        account_id: string;
        icp_id: string;
        persona_id: string | null;
        research_type: string;
        time_filter: string;
        title: string;
        topic?: string;
        created_by: string;
      }) => {
        console.log('🚀 Server market research mutator called with ID:', id);
        console.log('🚀 Server market research mutator called with params:',    
          {id, 
          account_id, 
          icp_id, 
          persona_id, 
          research_type, 
          time_filter, 
          title, 
          topic,
          created_by} );

        // Call the client mutator to insert the initial record
        await clientMutators.generated_research.insert(tx, {
          id,
          account_id,
          icp_id,
          persona_id: persona_id || null,
          research_type,
          time_filter,
          title,
          topic,
          created_by,
        });

        // ✅ Queue async work to be done AFTER the transaction commits
        asyncTasks.push(async () => {

          try {
            console.log('Starting market research generation for ID:', id);

            // Fetch ICP and persona data
            const icpResult = await sql`
              SELECT data FROM icps WHERE id = ${icp_id}
            `;
            let personaResult = null;
            if(persona_id) {
              personaResult = await sql`
                SELECT data FROM personas WHERE id = ${persona_id}
              `;
            } 
          

            if (icpResult.length === 0) {
              throw new Error('ICP or Persona not found');
            }
            console.log('icpResult', icpResult);
            const icpData = icpResult[0];
            const personaData = personaResult ? personaResult[0] : null;

            // Combine ICP and persona data
            const combinedData = `ICP Data: ${JSON.stringify(icpData.data)}\n\nPersona Data: ${JSON.stringify(personaData?.data)}`;
            console.log('combinedData', combinedData);
            console.log('icpData', icpData.data);
            console.log('personaData', personaData?.data);
            // Generate market research
            const researchResult = await generateMarketResearch({
              icp_data: JSON.stringify(icpData.data),
              persona_data: JSON.stringify(personaData?.data),
              type: research_type,
              timeFilter: time_filter,
              topic,
            });

            // Update the record with results and set is_generating to false
            await sql`
              UPDATE generated_research 
              SET 
                results = ${sql.json(researchResult.research)},
                content_suggestions = ${sql.json(researchResult.content_suggestions)},
                is_generating = false,
                updated_at = ${Date.now()}
              WHERE id = ${id}
            `;

            console.log('✅ Market research generated and updated for ID:', id);
          } catch (error) {
            console.error('Failed to generate market research:', error);
            
            // Update the record to indicate failure
            await sql`
              UPDATE generated_research 
              SET 
                is_generating = false,
                updated_at = ${Date.now()}
              WHERE id = ${id}
            `;
          }
        });
      },
    },
    saved_research: {
      insert: async (tx, { 
        id, 
        account_id, 
        icp_id, 
        persona_id, 
        research_type, 
        time_filter, 
        title, 
        topic,
        description,
        source,
        source_url
      }: {
        id: string;
        account_id: string;
        icp_id: string;
        persona_id: string;
        research_type: string;
        time_filter: string;
        title: string;
        topic?: string;
        created_by: string;
        description: string;
        source: string;
        source_url: string;
      }) => {

        await tx.mutate.saved_research.insert({
          id,
          account_id,
          icp_id,
          persona_id,
          research_type,
          time_filter,
          title,
          topic,
          description,
          source,
          source_url
        });

        asyncTasks.push(async () => {
          const scrapedWebsite = await scrapeWebsiteByElement(source_url, ['main_article'], '{content: string}', 'Extract the main article from the website');
          console.log('scrapedWebsite', scrapedWebsite);
          await sql`
            UPDATE saved_research 
            SET 
              source_content = ${sql.json(scrapedWebsite.content)},
              updated_at = ${Date.now()}
            WHERE id = ${id}
          `;
        });
      },
    },
    icps: {
      ...clientMutators.icps,
      insert: async (tx: any, { 
        id, 
        values
      }: {
        id: string;
        values : ICPInsert;
      }) => {
        const now = Date.now();

        await tx.mutate.icps.insert({
          id,
          ...values,
          created_at: now,
        });

        if(values.withAi) {
          asyncTasks.push(async () => {

            await icpWithAiInsert(sql, {
              id,
              values
            });
          });
        }
       
      },
    },
    post_engagement_details: {
      ...clientMutators.post_engagement_details,
      insert: async (tx: any, { 
        id, 
        values
      }: {
        id: string;
        values: any;
      }) => {
        console.log('🚀 Server post_engagement_details insert mutator called with ID:', id, {values});
        await tx.mutate.post_engagement_details.insert({
          id,
          ...values
        });
        asyncTasks.push(async () => {

          const { profile_id, engaged_users, company_id } = values;
          try {
          // Ensure engaged_users is an array
          let engagedUsersArray = engaged_users;
          if (typeof engaged_users === 'string') {
            try {
              engagedUsersArray = JSON.parse(engaged_users);
            } catch (parseError) {
              console.error('Failed to parse engaged_users string:', parseError);
              engagedUsersArray = [];
            }
          }
          if (!Array.isArray(engagedUsersArray)) {
            console.error('engaged_users is not an array:', typeof engagedUsersArray);
            engagedUsersArray = [];
          }
          console.log('Processed engaged_users:', engagedUsersArray);

          //get all icp data and stringify the data field
          
          const icpData = await sql`
            SELECT data FROM icps WHERE company_id = ${company_id}
          `;
          const icpDataString = JSON.stringify(icpData);
          console.log('icpDataString', icpDataString);

          // get all the product documents, and join and stringify the content field
          const productDocuments = await sql`
            SELECT content FROM product_documents WHERE company_id = ${company_id}
          `;
          const productDocumentsString = productDocuments.map(doc => doc.content).join('\n\n');
          console.log('productDocumentsString', productDocumentsString);

          // for each engaged user, scrape the linkedin profile
          const engagedUserProfiles = await Promise.all(engagedUsersArray.map(async (user) => {
            console.log('user', user);
            const profile = await scrapeLinkedInProfile(user, sql);
            if(!profile) {
              return null;
            }
            return profile;
          }));
          
          // Filter out null profiles before generating the engagement report
          const validProfiles = engagedUserProfiles.filter(profile => profile !== null);
          console.log(`Successfully scraped ${validProfiles.length} out of ${engagedUsersArray.length} LinkedIn profiles`);
          
          const engagementReport = await generateEngagementReport(validProfiles, icpDataString, productDocumentsString);
          console.log('engagementReport', engagementReport, id);
          
          // Check if the engagement report generation failed
          if (engagementReport && engagementReport.error) {
            console.error('Engagement report generation returned error:', engagementReport.error);
            await sql`
              UPDATE post_engagement_details 
              SET 
                engagement_info = ${sql.json({ error: engagementReport.error, message: engagementReport.message })},
                is_generating = false,
                error_generating = true
              WHERE id = ${id}
            `;
            return;
          }
          
          // update the post_engagement_details table with the engagement report
          await sql`
            UPDATE post_engagement_details 
            SET 
              engagement_info = ${sql.json(engagementReport as any)},
              is_generating = false
            WHERE id = ${id}
          `;
            } catch (error) {
            console.error('Failed to generate engagement report:', error);
            await sql`
              UPDATE post_engagement_details 
              SET 
                is_generating = false,
                error_generating = true
              WHERE id = ${id}
            `;
          }
        });
      }
      },
    personas: {
      ...clientMutators.personas,
      insert: async (tx: any, { 
        id, 
        company_id,
        icp_id,
        withAi,
        name,
        data = {},
        error_generating = false
      }: {
        id: string;
        company_id: string;
        icp_id: string;
        withAi: boolean;
        name: string | null;
        data: any;
        error_generating: boolean;
      }) => {
        const now = Date.now();
        await tx.mutate.personas.insert({
          id,
          company_id,
          icp_id,
          created_at: now,
          is_generating: withAi ? true : false,
          name,
          data,
          error_generating,
        });
        if(withAi) {
                
          asyncTasks.push(async () => {
            try {
                //get the url from the accounts table
                const accountResult = await sql`
                  SELECT website FROM accounts WHERE id = ${company_id}
                `;
                const previousICPData = await sql`
                  SELECT * FROM icps WHERE id = ${icp_id}
                `;
                const previousPersonaData = await sql`
                  SELECT * FROM personas WHERE icp_id = ${icp_id}
                `;
                console.log('previousICPData', previousICPData, previousPersonaData);
                const accountData = accountResult[0];
                console.log('accountData', accountData);
                const website = accountData?.website;
                const personaData = await generatePersona(JSON.stringify(previousICPData));
                console.log('personaData', personaData);
                await sql`
                UPDATE personas 
                SET 
                  data = ${sql.json(personaData as any)},
                  name = ${personaData.name},
                  is_generating = false,
                  updated_at = ${Date.now()}
                WHERE id = ${id}
                `;
            } catch (error) {
              console.error('Failed to generate Persona:', error);
              await sql`
                UPDATE personas 
                SET 
                  is_generating = false,
                  error_generating = true
                WHERE id = ${id}
              `;
            }
            
          });
        }
      },
    },
    socials_research: {
      ...clientMutators.socials_research,
      insert: async (tx: any, { 
        id, 
        values
      }: {
        id: string;
        values: any;
      }) => {
        console.log('🚀 Server socials_research mutator called with ID:', id);
        console.log('🚀 Server socials_research mutator called with values:', values);

        // Extract scraping parameters (not to be stored in DB)
        const {
          onlyTwitterBlue,
          onlyVerifiedUsers,
          minimumFavorites,
          maxProfilesPerQuery,
          ...dbValues
        } = values;

        // Only store core database fields
        await tx.mutate.socials_research.insert({
          id,
          ...dbValues,
          is_generating: true,
          created_at: Date.now()
        });

        // ✅ Queue async work to be done AFTER the transaction commits
        asyncTasks.push(async () => {
          await socialResearchInsert(sql, {
            id,
            company_id: dbValues.company_id,
            keywords: dbValues.keywords,
            platform: dbValues.platform,
            // Pass platform-specific parameters for scraping only
            onlyTwitterBlue,
            onlyVerifiedUsers,
            minimumFavorites,
            maxProfilesPerQuery,
          });
        });
      },
    },
  };
}