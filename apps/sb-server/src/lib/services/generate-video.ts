import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import { GoogleAuth } from 'google-auth-library';
import dotenv from 'dotenv';

// Using Vertex AI instead of GoogleGenAI
// No more @google/genai import needed

dotenv.config();

interface GenerateVideoParams {
  video_prompt: string;
  model: 'veo-3-fast' | 'veo-3-standard';
  aspect_ratio?: string;
  resolution?: string;
  company_id: string;
  user_id: string;
  message_id?: string;
  conversation_id: string;
  image?: {
    imageBytes: Buffer;
    mimeType: string;
  };
}

interface GenerateVideoResult {
  operationId: string;
  operationName: string;
  jobId: string;
}

// Helper function to get access token
async function getAccessToken(): Promise<string> {
  let auth: GoogleAuth;
  
  if (process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);
    auth = new GoogleAuth({
      credentials,
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });
  } else if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY_PATH) {
    auth = new GoogleAuth({
      keyFilename: process.env.GOOGLE_SERVICE_ACCOUNT_KEY_PATH,
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });
  } else {
    throw new Error('No authentication method available');
  }
  
  const authClient = await auth.getClient();
  const accessToken = await authClient.getAccessToken();
  
  if (!accessToken.token) {
    throw new Error('Failed to get access token');
  }
  
  return accessToken.token;
}

// Get project ID from environment or service account
function getProjectId(): string {
  if (process.env.GOOGLE_CLOUD_PROJECT_ID) {
    return process.env.GOOGLE_CLOUD_PROJECT_ID;
  }
  
  if (process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
    const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);
    return credentials.project_id;
  }
  
  throw new Error('Project ID not found. Set GOOGLE_CLOUD_PROJECT_ID or use service account JSON with project_id');
}

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function generateVideo(params: GenerateVideoParams): Promise<GenerateVideoResult> {
  const {
    video_prompt,
    model,
    aspect_ratio = '16:9',
    // resolution = '720p',
    company_id,
    user_id,
    message_id,
    conversation_id,
    image
  } = params;

  console.log('🎬 [VIDEO-GEN] Starting video generation request');
  console.log('📋 [VIDEO-GEN] Parameters:', {
    prompt_length: video_prompt?.length,
    model,
    aspect_ratio,
    company_id,
    user_id,
    message_id,
    conversation_id,
    has_image: !!image
  });

  try {
    console.log('⚙️ [VIDEO-GEN] Initializing video generation with Veo 3:', { model, aspect_ratio });

    // Map our model names to Vertex AI model names
    const modelMap = {
      'veo-3-fast': 'veo-3.0-fast-generate-001',
      'veo-3-standard': 'veo-3.0-generate-001'
    };

    const vertexModel = modelMap[model];
    console.log('🔄 [VIDEO-GEN] Model mapping:', { requested: model, mapped: vertexModel });
    
    if (!vertexModel) {
      console.error('❌ [VIDEO-GEN] Unsupported model:', model);
      throw new Error(`Unsupported model: ${model}`);
    }

    // Create video generation job record
    const jobId = uuidv4();
    console.log('🆔 [VIDEO-GEN] Generated job ID:', jobId);
    
    // Prepare video generation config
    const config: any = {
      aspectRatio: aspect_ratio,
    };
    console.log('⚙️ [VIDEO-GEN] Generation config:', config);

    // Add resolution for Veo 3 models
    // if (model === 'veo-3-fast' || model === 'veo-3-standard') {
    // //   config.resolution = resolution;
    // }

    // Get project ID and access token
    const projectId = getProjectId();
    const accessToken = await getAccessToken();
    
    console.log('🏗️ [VIDEO-GEN] Project ID:', projectId);

    // Prepare Vertex AI request
    const instances = [{
      prompt: video_prompt,
      ...(image && {
        image: {
          bytesBase64Encoded: image.imageBytes.toString('base64'),
          mimeType: image.mimeType
        }
      })
    }];

    const parameters = {
      aspectRatio: aspect_ratio,
      generateAudio: true, // Required for Veo 3
      durationSeconds: 8,
      sampleCount: 1
    };

    const vertexRequest = {
      instances,
      parameters
    };

    console.log('📤 [VIDEO-GEN] Sending request to Vertex AI...');
    console.log('📋 [VIDEO-GEN] Request payload:', JSON.stringify({
      instances: [{
        prompt: `${video_prompt.substring(0, 100)}${video_prompt.length > 100 ? '...' : ''}`,
        ...(image && { image: '[IMAGE_DATA]' })
      }],
      parameters
    }, null, 2));

    // Make request to Vertex AI
    const vertexUrl = `https://us-central1-aiplatform.googleapis.com/v1/projects/${projectId}/locations/us-central1/publishers/google/models/${vertexModel}:predictLongRunning`;
    
    const response = await fetch(vertexUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json; charset=utf-8'
      },
      body: JSON.stringify(vertexRequest)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [VIDEO-GEN] Vertex AI request failed:', response.status, errorText);
      throw new Error(`Vertex AI request failed: ${response.status} ${response.statusText}. ${errorText}`);
    }

    const operation = await response.json();

    console.log('✅ [VIDEO-GEN] Video generation started successfully');
    console.log('🔄 [VIDEO-GEN] Operation details:', {
      name: operation.name,
      done: operation.done,
      metadata: operation.metadata
    });

    // Store the job in the database
    console.log('💾 [VIDEO-GEN] Saving job to database...');
    const jobRecord = {
      id: jobId,
      message_id,
      account_id: company_id,
      user_id,
      model,
      prompt: video_prompt,
      external_operation_id: operation.name,
      external_operation_name: operation.name,
      status: 'processing',
      generation_config: {
        aspect_ratio,
        model: vertexModel,
      },
      started_at: new Date().toISOString(),
    };
    
    console.log('📋 [VIDEO-GEN] Job record:', {
      ...jobRecord,
      prompt: `${video_prompt.substring(0, 50)}...`
    });

    const { data: jobData, error: jobError } = await supabase
      .from('video_generation_jobs')
      .insert(jobRecord)
      .select()
      .single();

    if (jobError) {
      console.error('❌ [VIDEO-GEN] Error saving video generation job:', jobError);
      throw new Error('Failed to save video generation job');
    }
    
    console.log('✅ [VIDEO-GEN] Job saved to database successfully:', jobData?.id);

    // Update the message to indicate generation is in progress
    if (message_id) {
      console.log('📝 [VIDEO-GEN] Updating message status to generating...');
      const messageUpdate = {
        is_generating: true,
        is_error: false,
        video_generation_id: jobId,
        video_generation_model: model,
        video_generation_status: 'processing',
        video_generation_prompt: video_prompt,
        generation_started_at: new Date().toISOString(),
      };
      
      const { error: messageError } = await supabase
        .from('video_messages')
        .update(messageUpdate)
        .eq('id', message_id);

      if (messageError) {
        console.error('❌ [VIDEO-GEN] Error updating message:', messageError);
        // Don't throw here, job is already started
      } else {
        console.log('✅ [VIDEO-GEN] Message status updated successfully');
      }
    }

    // Start polling for completion in the background
    console.log('🔄 [VIDEO-GEN] Starting background polling for job completion...');
    setImmediate(() => {
      pollVideoGenerationStatus(jobId, operation).catch(error => {
        console.error('❌ [VIDEO-GEN] Error in background polling:', error);
      });
    });

    console.log('🎉 [VIDEO-GEN] Video generation request completed successfully');
    const result = {
      operationId: operation.name,
      operationName: operation.name,
      jobId,
    };
    console.log('📤 [VIDEO-GEN] Returning result:', result);

    return result;

  } catch (error) {
    console.error('❌ [VIDEO-GEN] Error generating video:', error);
    
    // Update message with error if message_id is provided
    if (message_id) {
      await supabase
        .from('video_messages')
        .update({
          is_generating: false,
          is_error: true,
          generation_error_message: error instanceof Error ? error.message : 'Unknown error',
        })
        .eq('id', message_id)
        // .catch(console.error);
    }

    throw error;
  }
}

async function pollVideoGenerationStatus(jobId: string, initialOperation: any) {
  const maxPollingTime = 10 * 60 * 1000; // 10 minutes
  const pollingInterval = 10 * 1000; // 10 seconds
  const startTime = Date.now();

  console.log(`🔄 [POLLING] Starting polling for job ${jobId}`);
  console.log(`⏱️ [POLLING] Max polling time: ${maxPollingTime / 1000}s, interval: ${pollingInterval / 1000}s`);
  
  let operation = initialOperation;
  let pollCount = 0;

  while (Date.now() - startTime < maxPollingTime) {
    try {
      pollCount++;
      const elapsedTime = Math.round((Date.now() - startTime) / 1000);
      
      console.log(`🔍 [POLLING] Poll #${pollCount} for job ${jobId} (${elapsedTime}s elapsed)`);

      // Get access token and project ID for polling
      const accessToken = await getAccessToken();
      const projectId = getProjectId();
      
      // Extract operation ID from operation name
      const operationId = operation.name.split('/').pop();
      
      // Get the current operation status from Vertex AI
      const pollUrl = `https://us-central1-aiplatform.googleapis.com/v1/projects/${projectId}/locations/us-central1/publishers/google/models/${operation.name.includes('veo-3.0-fast') ? 'veo-3.0-fast-generate-001' : 'veo-3.0-generate-001'}:fetchPredictOperation`;
      
      const pollResponse = await fetch(pollUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json; charset=utf-8'
        },
        body: JSON.stringify({
          operationName: operation.name
        })
      });

      if (!pollResponse.ok) {
        const errorText = await pollResponse.text();
        console.error('❌ [POLLING] Poll request failed:', pollResponse.status, errorText);
        throw new Error(`Poll request failed: ${pollResponse.status} ${pollResponse.statusText}`);
      }

      operation = await pollResponse.json();

      console.log(`📊 [POLLING] Job ${jobId} status:`, {
        done: operation.done,
        hasResponse: !!operation.response,
        hasError: !!operation.error,
        responseKeys: operation.response ? Object.keys(operation.response) : [],
        metadata: operation.metadata
      });

      if (operation.done) {
        console.log(`✅ [POLLING] Job ${jobId} completed! Processing results...`);
        
        if (operation.response?.videos?.[0]) {
          console.log(`🎬 [POLLING] Video generation successful for job ${jobId}`);
          await handleVideoGenerationSuccess(jobId, operation);
        } else if (operation.error) {
          console.log(`❌ [POLLING] Video generation failed for job ${jobId}:`, operation.error);
          await handleVideoGenerationError(jobId, operation.error);
        } else {
          console.log(`⚠️ [POLLING] Unexpected operation state for job ${jobId}:`, operation);
          await handleVideoGenerationError(jobId, new Error('Unexpected operation state'));
        }
        break;
      }

      console.log(`⏳ [POLLING] Job ${jobId} still processing, waiting ${pollingInterval / 1000}s...`);
      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollingInterval));

    } catch (error) {
      console.error(`❌ [POLLING] Error polling job ${jobId}:`, error);
      await handleVideoGenerationError(jobId, error);
      break;
    }
  }

  // If we've exceeded max polling time
  if (Date.now() - startTime >= maxPollingTime) {
    const finalElapsedTime = Math.round((Date.now() - startTime) / 1000);
    console.error(`⏰ [POLLING] Polling timeout for job ${jobId} after ${finalElapsedTime}s (${pollCount} polls)`);
    await handleVideoGenerationError(jobId, new Error('Video generation timeout'));
  }
}

async function handleVideoGenerationSuccess(jobId: string, operation: any) {
  try {
    console.log(`🎉 [SUCCESS] Video generation completed for job ${jobId}`);
    console.log(`📋 [SUCCESS] Full operation response:`, JSON.stringify(operation.response, null, 2));

    const generatedVideo = operation.response.videos[0];
    console.log(`🎬 [SUCCESS] Extracted video object:`, JSON.stringify(generatedVideo, null, 2));
    
    // Download the video from Vertex AI (Cloud Storage)
    console.log('📥 [DOWNLOAD] Starting video download from Vertex AI...');
    
    let videoBuffer: Buffer;
    
    try {
      // Check if we have a Cloud Storage URI or base64 data
      if (generatedVideo.gcsUri) {
        // Method 1: Download from Cloud Storage
        console.log('🔧 [DOWNLOAD] Downloading from Cloud Storage:', generatedVideo.gcsUri);
        
        // Get access token for Cloud Storage
        const accessToken = await getAccessToken();
        
        // Convert gs:// URI to HTTP URL for download
        // gs://bucket/path -> https://storage.googleapis.com/bucket/path
        const httpUrl = generatedVideo.gcsUri.replace('gs://', 'https://storage.googleapis.com/');
        
        console.log('🌐 [DOWNLOAD] Converted URL:', httpUrl);
        
        const fetchResponse = await fetch(httpUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });
        
        console.log('📊 [DOWNLOAD] Cloud Storage response:', {
          status: fetchResponse.status,
          statusText: fetchResponse.statusText,
          contentType: fetchResponse.headers.get('content-type'),
          contentLength: fetchResponse.headers.get('content-length'),
        });
        
        if (!fetchResponse.ok) {
          const errorText = await fetchResponse.text();
          console.log('❌ [DOWNLOAD] Cloud Storage download failed:', errorText);
          throw new Error(`Cloud Storage download failed: ${fetchResponse.status} ${fetchResponse.statusText}. Response: ${errorText}`);
        }
        
        // Convert response to buffer
        console.log('📊 [DOWNLOAD] Converting response to buffer...');
        const arrayBuffer = await fetchResponse.arrayBuffer();
        videoBuffer = Buffer.from(arrayBuffer);
        console.log('✅ [DOWNLOAD] Cloud Storage download successful');
        
      } else if (generatedVideo.bytesBase64Encoded) {
        // Method 2: Decode base64 data directly
        console.log('🔧 [DOWNLOAD] Processing base64-encoded video data');
        console.log('📏 [DOWNLOAD] Base64 string length:', generatedVideo.bytesBase64Encoded.length, 'characters');
        
        // Decode base64 to buffer
        videoBuffer = Buffer.from(generatedVideo.bytesBase64Encoded, 'base64');
        console.log('✅ [DOWNLOAD] Base64 decoding successful');
        
      } else {
        throw new Error('No video data found - neither gcsUri nor bytesBase64Encoded present');
      }
    } catch (downloadError) {
      console.error('❌ [DOWNLOAD] Download error:', downloadError);
      throw new Error(`Failed to download video: ${downloadError.message}`);
    }
    
    console.log('🎊 [DOWNLOAD] Video downloaded successfully!');
    console.log('📏 [DOWNLOAD] Buffer size:', videoBuffer.length, 'bytes', `(${(videoBuffer.length / 1024 / 1024).toFixed(2)} MB)`);

    // Upload to Supabase Storage
    const videoFileName = `generated-videos/${jobId}.mp4`;
    console.log('☁️ [UPLOAD] Starting upload to Supabase Storage...');
    console.log('📁 [UPLOAD] File path:', videoFileName);
    console.log('📏 [UPLOAD] File size:', videoBuffer.length, 'bytes');
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('videos')
      .upload(videoFileName, videoBuffer, {
        contentType: 'video/mp4',
        upsert: true,
      });

    if (uploadError) {
      console.error('❌ [UPLOAD] Upload failed:', uploadError);
      throw new Error(`Failed to upload video: ${uploadError.message}`);
    }

    console.log('✅ [UPLOAD] Upload successful:', uploadData);

    // Get public URL
    console.log('🔗 [UPLOAD] Getting public URL...');
    const { data: urlData } = supabase.storage
      .from('videos')
      .getPublicUrl(videoFileName);

    const videoUrl = urlData.publicUrl;
    console.log('🌐 [UPLOAD] Public URL generated:', videoUrl);

    // Update job status
    console.log('💾 [DATABASE] Updating job status to completed...');
    const jobUpdate = {
      status: 'completed',
      generated_video_url: videoUrl,
      video_storage_path: videoFileName,
      completed_at: new Date().toISOString(),
    };
    
    const { error: jobError } = await supabase
      .from('video_generation_jobs')
      .update(jobUpdate)
      .eq('id', jobId);

    if (jobError) {
      console.error('❌ [DATABASE] Error updating job status:', jobError);
    } else {
      console.log('✅ [DATABASE] Job status updated successfully');
    }

    // Update message status
    console.log('🔍 [DATABASE] Getting message ID from job...');
    const { data: jobData } = await supabase
      .from('video_generation_jobs')
      .select('message_id')
      .eq('id', jobId)
      .single();

    console.log('📋 [DATABASE] Job data:', jobData);

    if (jobData?.message_id) {
      console.log('📝 [DATABASE] Updating message status to completed...');
      const messageUpdate = {
        is_generating: false,
        is_error: false,
        video_generation_status: 'completed',
        generated_video_url: videoUrl,
        generation_completed_at: new Date().toISOString(),
      };
      
      const { error: messageError } = await supabase
        .from('video_messages')
        .update(messageUpdate)
        .eq('id', jobData.message_id);

      if (messageError) {
        console.error('❌ [DATABASE] Error updating message status:', messageError);
      } else {
        console.log('✅ [DATABASE] Message status updated successfully');
      }

      // Create a video-only user message for better UX
      console.log('📱 [DATABASE] Creating video-only message for completed generation...');
      
      // Get the original message to extract conversation details
      const { data: originalMessage } = await supabase
        .from('video_messages')
        .select('conversation_id, user_id, company_id')
        .eq('id', jobData.message_id)
        .single();

      if (originalMessage) {
        const videoMessageId = uuidv4();
        
        const videoMessage = {
          id: videoMessageId,
          conversation_id: originalMessage.conversation_id,
          role: 'user',
          content: '', // Empty content - just the video
          user_id: originalMessage.user_id,
          company_id: originalMessage.company_id,
          generated_video_url: videoUrl,
          // video_generation_model: jobData.model || 'veo-3-fast',
          video_generation_status: 'completed',
          is_generating: false,
          is_error: false,
          created_at: new Date().toISOString(),
        };

        const { error: videoMessageError } = await supabase
          .from('video_messages')
          .insert(videoMessage);

        if (videoMessageError) {
          console.error('❌ [DATABASE] Error creating video-only message:', videoMessageError);
        } else {
          console.log('✅ [DATABASE] Video-only message created successfully:', videoMessageId);
        }
      } else {
        console.log('⚠️ [DATABASE] Could not retrieve original message for video-only message creation');
      }
    } else {
      console.log('⚠️ [DATABASE] No message ID found for job');
    }

    console.log(`🎉 [SUCCESS] Video generation completed successfully for job ${jobId}`);
    console.log(`🔗 [SUCCESS] Video URL: ${videoUrl}`);

  } catch (error) {
    console.error(`Error handling video generation success for job ${jobId}:`, error);
    await handleVideoGenerationError(jobId, error);
  }
}

async function handleVideoGenerationError(jobId: string, error: any) {
  try {
    console.error(`💥 [ERROR] Video generation failed for job ${jobId}:`, error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.log(`📝 [ERROR] Error message: ${errorMessage}`);

    // Update job status
    console.log('💾 [ERROR] Updating job status to failed...');
    const jobUpdate = {
      status: 'failed',
      error_message: errorMessage,
      completed_at: new Date().toISOString(),
    };
    
    const { error: jobError } = await supabase
      .from('video_generation_jobs')
      .update(jobUpdate)
      .eq('id', jobId);

    if (jobError) {
      console.error('❌ [ERROR] Error updating job status:', jobError);
    } else {
      console.log('✅ [ERROR] Job status updated to failed');
    }

    // Update message status
    console.log('🔍 [ERROR] Getting message ID from job...');
    const { data: jobData } = await supabase
      .from('video_generation_jobs')
      .select('message_id')
      .eq('id', jobId)
      .single();

    console.log('📋 [ERROR] Job data:', jobData);

    if (jobData?.message_id) {
      console.log('📝 [ERROR] Updating message status to failed...');
      const messageUpdate = {
        is_generating: false,
        is_error: true,
        video_generation_status: 'failed',
        generation_error_message: errorMessage,
        generation_completed_at: new Date().toISOString(),
      };
      
      const { error: messageError } = await supabase
        .from('video_messages')
        .update(messageUpdate)
        .eq('id', jobData.message_id);

      if (messageError) {
        console.error('❌ [ERROR] Error updating message status:', messageError);
      } else {
        console.log('✅ [ERROR] Message status updated to failed');
      }
    } else {
      console.log('⚠️ [ERROR] No message ID found for job');
    }

    console.log(`💥 [ERROR] Error handling completed for job ${jobId}`);

  } catch (updateError) {
    console.error(`🚨 [ERROR] Critical error updating job ${jobId} with error status:`, updateError);
  }
}
