name: Deploy Orchestra<PERSON>

on:
  push:
    branches:
      - main
      - staging
      - preview
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment (preview | staging | production)'
        required: false
        type: choice
        options:
          - preview
          - staging
          - production

jobs:
  resolve_env:
    name: Resolve environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.setenv.outputs.environment }}
    steps:
      - name: Set environment from branch or input
        id: setenv
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ] && [ -n "${{ github.event.inputs.environment }}" ]; then
            ENVIRONMENT="${{ github.event.inputs.environment }}"
          elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
            ENVIRONMENT="production"
          elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
            ENVIRONMENT="staging"
          elif [ "${{ github.ref }}" = "refs/heads/preview" ]; then
            ENVIRONMENT="preview"
          else
            echo "❌ Unsupported branch: ${{ github.ref_name }}"
            echo "Only main, staging, and preview branches are supported for automatic deployment"
            exit 1
          fi

          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "✅ Resolved environment: $ENVIRONMENT"

  detect_changes:
    name: Detect changes
    runs-on: ubuntu-latest
    outputs:
      supabase: ${{ steps.filter.outputs.supabase }}
      zero_server: ${{ steps.filter.outputs.zero_server }}
      sb_server: ${{ steps.filter.outputs.sb_server }}
      supabase_fallback: ${{ steps.fallback.outputs.supabase_fallback }}
      zero_server_fallback: ${{ steps.fallback.outputs.zero_server_fallback }}
      sb_server_fallback: ${{ steps.fallback.outputs.sb_server_fallback }}
      deploy_supabase: ${{ steps.decision.outputs.deploy_supabase }}
      deploy_zero_server: ${{ steps.decision.outputs.deploy_zero_server }}
      deploy_sb_server: ${{ steps.decision.outputs.deploy_sb_server }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Paths filter
        id: filter
        uses: dorny/paths-filter@v3
        continue-on-error: true
        with:
          filters: |
            supabase:
              - 'apps/web/supabase/**'
            zero_server:
              - 'apps/zero-server/**'
              - 'packages/zero-schema/**'
            sb_server:
              - 'apps/sb-server/**'
              - 'packages/zero-schema/**'

      - name: Fallback change detection
        id: fallback
        run: |
          SB_CHANGES=$(git diff --name-only HEAD~1 HEAD 2>/dev/null | grep -E '^apps/sb-server/' | wc -l || echo "0")
          ZERO_CHANGES=$(git diff --name-only HEAD~1 HEAD 2>/dev/null | grep -E '^apps/zero-server/' | wc -l || echo "0")
          SUPABASE_CHANGES=$(git diff --name-only HEAD~1 HEAD 2>/dev/null | grep -E '^apps/web/supabase/' | wc -l || echo "0")
          SCHEMA_CHANGES=$(git diff --name-only HEAD~1 HEAD 2>/dev/null | grep -E '^packages/zero-schema/' | wc -l || echo "0")
          
          echo "supabase_fallback=$([[ $SUPABASE_CHANGES -gt 0 ]] && echo true || echo false)" >> $GITHUB_OUTPUT
          echo "zero_server_fallback=$([[ $ZERO_CHANGES -gt 0 || $SCHEMA_CHANGES -gt 0 ]] && echo true || echo false)" >> $GITHUB_OUTPUT
          echo "sb_server_fallback=$([[ $SB_CHANGES -gt 0 || $SCHEMA_CHANGES -gt 0 ]] && echo true || echo false)" >> $GITHUB_OUTPUT

      - name: Make deployment decisions
        id: decision
        run: |
          DEPLOY_SUPABASE="false"
          DEPLOY_ZERO="false"
          DEPLOY_SB="false"
          
          # Check if Supabase needs deployment
          if [[ "${{ steps.filter.outputs.supabase }}" == "true" || "${{ steps.fallback.outputs.supabase_fallback }}" == "true" ]]; then
            DEPLOY_SUPABASE="true"
          fi
          
          # Check if Zero Server needs deployment
          if [[ "${{ steps.filter.outputs.zero_server }}" == "true" || "${{ steps.fallback.outputs.zero_server_fallback }}" == "true" ]]; then
            DEPLOY_ZERO="true"
          fi
          
          # Check if SB Server needs deployment
          if [[ "${{ steps.filter.outputs.sb_server }}" == "true" || "${{ steps.fallback.outputs.sb_server_fallback }}" == "true" ]]; then
            DEPLOY_SB="true"
          fi
          
          echo "deploy_supabase=$DEPLOY_SUPABASE" >> $GITHUB_OUTPUT
          echo "deploy_zero_server=$DEPLOY_ZERO" >> $GITHUB_OUTPUT
          echo "deploy_sb_server=$DEPLOY_SB" >> $GITHUB_OUTPUT
          
          echo "🎯 Deployment Plan:"
          echo "  Supabase: $DEPLOY_SUPABASE"
          echo "  Zero Server: $DEPLOY_ZERO"
          echo "  SB Server: $DEPLOY_SB"

  # Phase 1: Deploy Supabase (must complete first)
  deploy_supabase:
    name: Deploy Supabase
    needs: [resolve_env, detect_changes]
    if: needs.detect_changes.outputs.deploy_supabase == 'true'
    uses: ./.github/workflows/deploy-supabase.yml
    with:
      environment: ${{ needs.resolve_env.outputs.environment }}
    secrets: inherit

  # Phase 2: Deploy Zero Server (after Supabase)
  deploy_zero_server:
    name: Deploy Zero Server
    needs: [resolve_env, detect_changes, deploy_supabase]
    if: |
      always() && 
      needs.detect_changes.outputs.deploy_zero_server == 'true' &&
      (needs.deploy_supabase.result == 'success' || needs.deploy_supabase.result == 'skipped')
    uses: ./.github/workflows/deploy-zero-server.yml
    with:
      environment: ${{ needs.resolve_env.outputs.environment }}
    secrets: inherit

  # Phase 3: Deploy SB Server (after Supabase)
  deploy_sb_server:
    name: Deploy SB Server
    needs: [resolve_env, detect_changes, deploy_supabase, deploy_zero_server]
    if: |
      always() && 
      (needs.deploy_supabase.result == 'success' || needs.deploy_supabase.result == 'skipped') &&
      (needs.deploy_zero_server.result == 'success' || needs.deploy_zero_server.result == 'skipped') &&
      needs.detect_changes.outputs.deploy_sb_server == 'true'
    uses: ./.github/workflows/deploy-sb-server.yml
    with:
      environment: ${{ needs.resolve_env.outputs.environment }}
    secrets: inherit

  # Summary job
  deployment_summary:
    name: Deployment Summary
    needs: [resolve_env, detect_changes, deploy_supabase, deploy_zero_server, deploy_sb_server]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Generate deployment summary
        run: |
          echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** ${{ needs.resolve_env.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Service Deployment Status" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Supabase status
          if [[ "${{ needs.detect_changes.outputs.deploy_supabase }}" == "true" ]]; then
            if [[ "${{ needs.deploy_supabase.result }}" == "success" ]]; then
              echo "✅ **Supabase:** Deployed successfully" >> $GITHUB_STEP_SUMMARY
            else
              echo "❌ **Supabase:** Failed to deploy" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "⏭️ **Supabase:** No changes detected, skipped" >> $GITHUB_STEP_SUMMARY
          fi
          
          # Zero Server status
          if [[ "${{ needs.detect_changes.outputs.deploy_zero_server }}" == "true" ]]; then
            if [[ "${{ needs.deploy_zero_server.result }}" == "success" ]]; then
              echo "✅ **Zero Server:** Deployed successfully" >> $GITHUB_STEP_SUMMARY
            elif [[ "${{ needs.deploy_zero_server.result }}" == "failure" ]]; then
              echo "❌ **Zero Server:** Failed to deploy" >> $GITHUB_STEP_SUMMARY
            else
              echo "⏸️ **Zero Server:** Skipped due to Supabase failure" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "⏭️ **Zero Server:** No changes detected, skipped" >> $GITHUB_STEP_SUMMARY
          fi
          
          # SB Server status
          if [[ "${{ needs.detect_changes.outputs.deploy_sb_server }}" == "true" ]]; then
            if [[ "${{ needs.deploy_sb_server.result }}" == "success" ]]; then
              echo "✅ **SB Server:** Deployed successfully" >> $GITHUB_STEP_SUMMARY
            elif [[ "${{ needs.deploy_sb_server.result }}" == "failure" ]]; then
              echo "❌ **SB Server:** Failed to deploy" >> $GITHUB_STEP_SUMMARY
            else
              echo "⏸️ **SB Server:** Skipped due to Supabase failure" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "⏭️ **SB Server:** No changes detected, skipped" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Check for failures
        run: |
          FAILED_SERVICES=""
          
          if [[ "${{ needs.deploy_supabase.result }}" == "failure" ]]; then
            FAILED_SERVICES="$FAILED_SERVICES Supabase"
          fi
          
          if [[ "${{ needs.deploy_zero_server.result }}" == "failure" ]]; then
            FAILED_SERVICES="$FAILED_SERVICES Zero-Server"
          fi
          
          if [[ "${{ needs.deploy_sb_server.result }}" == "failure" ]]; then
            FAILED_SERVICES="$FAILED_SERVICES SB-Server"
          fi
          
          if [[ -n "$FAILED_SERVICES" ]]; then
            echo "❌ Deployment failed for:$FAILED_SERVICES"
            echo "Please check the individual job logs for detailed error information."
            exit 1
          else
            echo "✅ All deployments completed successfully!"
          fi

