name: Deploy SB Server

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment (preview | staging | production)'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 25

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9.12.0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'pnpm'
        cache-dependency-path: 'pnpm-lock.yaml'

    - name: Cache dependencies and build
      uses: actions/cache@v4
      with:
        path: |
          ~/.pnpm-store
          apps/sb-server/dist
          packages/zero-schema/dist
        key: ${{ runner.os }}-sb-server-${{ hashFiles('**/pnpm-lock.yaml', 'apps/sb-server/src/**/*', 'packages/zero-schema/src/**/*') }}
        restore-keys: |
          ${{ runner.os }}-sb-server-

    - name: Install dependencies
      run: pnpm install --no-frozen-lockfile

    - name: Wait for database stability
      run: |
        echo "⏳ Waiting for database to stabilize after Supabase deployment..."
        sleep 30

    - name: Build sb-server with retry
      id: build
      run: |
        cd apps/sb-server
        
        MAX_RETRIES=2
        RETRY_COUNT=0
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "🔨 Build attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
          
          if [ ! -f "dist/index.js" ]; then
            echo "🔨 Building from scratch..."
            if pnpm run build; then
              echo "✅ Build successful!"
              break
            else
              RETRY_COUNT=$((RETRY_COUNT + 1))
              if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                echo "⚠️ Build failed, cleaning and retrying..."
                rm -rf dist node_modules/.cache
                sleep 10
              else
                echo "❌ Build failed after all retries"
                exit 1
              fi
            fi
          else
            echo "✅ Using cached build output"
            pnpm run setup-zero-schema
            break
          fi
        done
        
        # Verify build output
        if [ -f "dist/index.js" ]; then
          echo "✅ Build verification passed"
          echo "build_success=true" >> $GITHUB_OUTPUT
        else
          echo "❌ Build verification failed"
          echo "build_success=false" >> $GITHUB_OUTPUT
          exit 1
        fi

    - name: Authenticate to Google Cloud
      if: steps.build.outputs.build_success == 'true'
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: '${{ secrets.GOOGLE_CLOUD_CREDENTIALS }}'

    - name: Set up Cloud SDK
      if: steps.build.outputs.build_success == 'true'
      uses: 'google-github-actions/setup-gcloud@v2'
      with:
        version: 'latest'

    - name: Pre-deployment database check
      if: steps.build.outputs.build_success == 'true'
      run: |
        echo "🔍 Checking database connectivity before deployment..."
        
        # Simple connection test
        timeout 30s node -e "
          const { Client } = require('pg');
          const client = new Client({
            connectionString: process.env.TEST_DB_URL,
            connectionTimeoutMillis: 10000,
          });
          client.connect()
            .then(() => {
              console.log('✅ Database connection test passed');
              return client.end();
            })
            .catch(err => {
              console.log('⚠️ Database connection test failed:', err.message);
              console.log('Continuing with deployment...');
            });
        " || echo "⚠️ Connection test inconclusive, proceeding..."
      env:
        TEST_DB_URL: ${{ secrets[format('SUPABASE_CONNECTION_{0}', inputs.environment == 'preview' && 'DEV' || inputs.environment == 'staging' && 'STAGING' || 'PROD')] }}

    - name: Deploy with retry and connection management
      if: steps.build.outputs.build_success == 'true'
      id: deploy
      run: |
        cd apps/sb-server
        
        ENVIRONMENT="${{ inputs.environment }}"
        MAX_RETRIES=3
        RETRY_COUNT=0
        
        echo "🚀 Deploying SB Server to: $ENVIRONMENT"
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "🚀 Deployment attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
          
          # Set deployment script based on environment
          case "$ENVIRONMENT" in
            preview)
              DEPLOY_SCRIPT="./deploy-preview-nodejs.sh"
              APP_URL="https://preview-dot-psychic-valve-439013-d2.lm.r.appspot.com"
              ;;
            staging)
              DEPLOY_SCRIPT="./deploy-staging-nodejs.sh"
              APP_URL="https://staging-dot-psychic-valve-439013-d2.lm.r.appspot.com"
              ;;
            production)
              DEPLOY_SCRIPT="./deploy-production-nodejs.sh"
              chmod +x "$DEPLOY_SCRIPT"
              APP_URL="https://psychic-valve-439013-d2.lm.r.appspot.com"
              ;;
          esac
          
          # Add connection cleanup before deployment
          echo "🧹 Cleaning up any stale connections..."
          sleep 5
          
          if timeout 600s $DEPLOY_SCRIPT; then
            echo "✅ SB Server deployment successful!"
            echo "deployment_success=true" >> $GITHUB_OUTPUT
            echo "app_url=$APP_URL" >> $GITHUB_OUTPUT
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⚠️ Deployment failed, waiting before retry..."
              sleep 60
            else
              echo "❌ All deployment attempts failed"
              echo "deployment_success=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          fi
        done

    - name: Post-deployment verification
      if: steps.deploy.outputs.deployment_success == 'true'
      run: |
        echo "🔍 Verifying SB Server deployment..."
        
        # Wait for service to be ready
        sleep 30
        
        APP_URL="${{ steps.deploy.outputs.app_url }}"
        
        # Test service health with retry
        MAX_RETRIES=5
        RETRY_COUNT=0
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          if curl -f --max-time 30 "$APP_URL/health" 2>/dev/null; then
            echo "✅ SB Server health check passed"
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⏳ Health check failed, retrying in 15 seconds..."
              sleep 15
            else
              echo "⚠️ Health check failed after all retries, but deployment completed"
            fi
          fi
        done

    - name: Comment on PR (if applicable)
      if: github.event_name == 'pull_request' && inputs.environment == 'preview' && steps.deploy.outputs.deployment_success == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 **SB Server Preview Deployed!**\n\n📍 **URL:** ${{ steps.deploy.outputs.app_url }}\n\n✅ Your changes are now live in the preview environment.'
          })

    - name: Add deployment summary
      if: always()
      run: |
        echo "## 🚀 SB Server Deployment" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ inputs.environment }}" >> $GITHUB_STEP_SUMMARY
        echo "**Build Status:** ${{ steps.build.outputs.build_success == 'true' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "**Deployment Status:** ${{ steps.deploy.outputs.deployment_success == 'true' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        if [[ "${{ steps.deploy.outputs.deployment_success }}" == "true" ]]; then
          echo "**Service URL:** ${{ steps.deploy.outputs.app_url }}" >> $GITHUB_STEP_SUMMARY
        fi

    - name: Cleanup on failure
      if: failure()
      run: |
        echo "🧹 Cleaning up failed deployment..."
        cd apps/sb-server
        rm -rf deploy-temp dist/temp
        echo "Please check the logs above for detailed error information"
