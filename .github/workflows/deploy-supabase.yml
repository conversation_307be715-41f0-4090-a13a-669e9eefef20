name: Deploy Supabase Migrations

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment (preview | staging | production)'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 15

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Cache Supabase CLI
      uses: actions/cache@v4
      with:
        path: ~/.cache/supabase
        key: ${{ runner.os }}-supabase-cli-${{ hashFiles('apps/web/supabase/config.toml') }}
        restore-keys: |
          ${{ runner.os }}-supabase-cli-

    - name: Install Supabase CLI
      uses: supabase/setup-cli@v1
      with:
        version: latest

    - name: Set environment variables
      id: env
      run: |
        ENVIRONMENT="${{ inputs.environment }}"
        echo "Deploying to: $ENVIRONMENT"
        echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
        
        # Set database URL based on environment
        case "$ENVIRONMENT" in
          production)
            echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_PRODUCTION }}@db.${{ secrets.SUPABASE_PROJECT_ID_PRODUCTION }}.supabase.co:5432/postgres" >> $GITHUB_ENV
            echo "project_id=${{ secrets.SUPABASE_PROJECT_ID_PRODUCTION }}" >> $GITHUB_OUTPUT
            ;;
          staging)
            echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_STAGING }}@db.${{ secrets.SUPABASE_PROJECT_ID_STAGING }}.supabase.co:5432/postgres" >> $GITHUB_ENV
            echo "project_id=${{ secrets.SUPABASE_PROJECT_ID_STAGING }}" >> $GITHUB_OUTPUT
            ;;
          preview)
            echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_PREVIEW }}@db.qbdtpzsbkacowlbdoolk.supabase.co:5432/postgres" >> $GITHUB_ENV
            echo "project_id=qbdtpzsbkacowlbdoolk" >> $GITHUB_OUTPUT
            ;;
          *)
            echo "❌ Unsupported environment: $ENVIRONMENT"
            exit 1
            ;;
        esac

    - name: Authenticate with Supabase
      run: |
        echo "🔑 Authenticating with Supabase..."
        supabase login --token ${{ secrets.SUPABASE_ACCESS_TOKEN }}

    - name: Pre-deployment validation
      run: |
        cd apps/web
        
        echo "📋 Validating migration files..."
        if [ ! -d "supabase/migrations" ]; then
          echo "❌ No migrations directory found"
          exit 1
        fi
        
        MIGRATION_COUNT=$(find supabase/migrations -name "*.sql" | wc -l)
        echo "Found $MIGRATION_COUNT migration files"
        
        if [ $MIGRATION_COUNT -eq 0 ]; then
          echo "⚠️ No migration files found, but continuing..."
        else
          echo "📋 Migration files:"
          ls -la supabase/migrations/
        fi

    - name: Deploy migrations with retry
      id: deploy
      run: |
        cd apps/web
        
        MAX_RETRIES=3
        RETRY_COUNT=0
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "🚀 Migration deployment attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
          
          if timeout 300s supabase db push --db-url ${{ env.SUPABASE_DB_URL }} --include-all; then
            echo "✅ Supabase migrations deployed successfully!"
            echo "deployment_success=true" >> $GITHUB_OUTPUT
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⚠️ Migration deployment failed, retrying in 30 seconds..."
              sleep 30
            else
              echo "❌ All migration deployment attempts failed"
              echo "deployment_success=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          fi
        done

    - name: Post-deployment verification
      if: steps.deploy.outputs.deployment_success == 'true'
      run: |
        echo "🔍 Verifying database state..."
        
        # Wait for migrations to settle
        sleep 15
        
        # Test database connectivity
        if timeout 30s psql "${{ env.SUPABASE_DB_URL }}" -c "SELECT 1;" > /dev/null 2>&1; then
          echo "✅ Database connectivity verified"
        else
          echo "⚠️ Database connectivity test failed, but migrations completed"
        fi

    - name: Add deployment summary
      if: always()
      run: |
        echo "## 🗄️ Supabase Migrations Deployed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
        echo "**Project ID:** ${{ steps.env.outputs.project_id }}" >> $GITHUB_STEP_SUMMARY
        echo "**Status:** ${{ steps.deploy.outputs.deployment_success == 'true' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY

    - name: Cleanup on failure
      if: failure()
      run: |
        echo "🧹 Cleaning up failed migration..."
        echo "Please check the migration logs above for detailed error information"
        echo "You may need to manually verify the database state"
